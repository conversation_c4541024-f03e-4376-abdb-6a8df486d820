package com.io661.extension.util.Notify;



import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class NotifyManager {

    public static void showTrayIcon() {
        if (SystemTray.isSupported()) {
            SystemTray tray = SystemTray.getSystemTray();

            Image image = Toolkit.getDefaultToolkit().getImage("src/main/resources/com/io661/extension/img/Logo.png");

            PopupMenu popup = new PopupMenu();

            MenuItem exitItem = new MenuItem("Exit");
            exitItem.addActionListener(new ActionListener() {
                public void actionPerformed(ActionEvent e) {
                    System.exit(0);
                }
            });
            popup.add(exitItem);

            TrayIcon trayIcon = new TrayIcon(image, "Notification Tray", popup);
            trayIcon.setImage(image);
        }
    }
}

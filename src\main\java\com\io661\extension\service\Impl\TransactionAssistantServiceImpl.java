package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.CommonResult;
import com.io661.extension.model.Steam.*;
import com.io661.extension.model.YouPin.YouPinUserInventoryOnSellDataListRes;
import com.io661.extension.model.YouPin.YouPinUserItemsOffSaleReq;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.service.YouPinService;
import com.io661.extension.util.User.UserCookieManager;
import lombok.Data;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.io661.extension.util.YouPin.YouPinCookieManager.readYouPinCookie;

@Data
public class TransactionAssistantServiceImpl implements TransactionAssistantService {
    private final CommonHttpUrl httpClient;
    private final YouPinService youPinService;

    public TransactionAssistantServiceImpl() {
        this.httpClient = new CommonHttpUrl();
        this.youPinService = new com.io661.extension.service.Impl.YouPinServiceImpl();
    }

    @Override
    public SteamInventoryResponse getInventoryList(Integer type, String steamId, Integer limit, Integer subType, boolean onSell, Integer sort, Integer page, String search) {

        SteamInventoryResponse result = new SteamInventoryResponse();

        String token = UserCookieManager.readToken();
        try {

            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            String onSellValue = onSell ? "true" : "false";

            // 处理搜索参数，确保不为null
            String searchParam = search != null ? search : "";

            // 对搜索参数进行URL编码
            String encodedSearch = java.net.URLEncoder.encode(searchParam, StandardCharsets.UTF_8);

            String endpoint = "web/inventory?type=" + type + "&steamId=" + steamId + "&limit=" + limit + "&subType=" + subType + "&onSell=" + onSellValue + "&sort=" + sort + "&page=" + page;

            // 只有当搜索参数不为空时才添加到URL中
            if (!searchParam.isEmpty()) {
                endpoint += "&search=" + encodedSearch;
            }

            String response = httpClient.doGet(endpoint, null, headers);

            // 使用GsonBuilder注册LocalDateTime适配器
            Gson gson = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                    .create();

            // 解析响应为 SteamInventoryResponse 类型
            SteamInventoryRes inventoryResponse = gson.fromJson(response, SteamInventoryRes.class);

            // 检查响应是否成功
            if (inventoryResponse != null && inventoryResponse.getCode() == 0 &&
                    inventoryResponse.getData() != null && inventoryResponse.getData().getList() != null) {

                result.setPages(inventoryResponse.getData().getPages());
                result.setTotal(inventoryResponse.getData().getTotal());
                result.setCount(inventoryResponse.getData().getCount());
                result.setList(inventoryResponse.getData().getList());

                return result;
            } else {
                System.out.println("获取库存失败: " + (inventoryResponse != null ? inventoryResponse.getMsg() : "响应为空"));
                return new SteamInventoryResponse();
            }

        }catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return result;
    }

    /**
     * 上架物品到市场
     *
     * @param request 上架请求
     * @return 上架响应
     */
    @Override
    public MarketListingResponse listItemsOnMarket(MarketListingRequest request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 发送POST请求
            String endpoint = "web/inventory/listing";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上架接口响应: " + response);

            // 解析响应
            MarketListingResponse listingResponse = gson.fromJson(response, MarketListingResponse.class);

            if (listingResponse != null && listingResponse.getCode() == 0) {
                System.out.println("上架成功: " + (listingResponse.getData() != null ?
                        "成功: " + listingResponse.getData().getSuccessCount() +
                                ", 失败: " + listingResponse.getData().getFailCount() : "无数据"));
            } else {
                System.out.println("上架失败: " + (listingResponse != null ? listingResponse.getMsg() : "响应为空"));
            }

            return listingResponse;
        } catch (Exception e) {
            System.out.println("上架物品异常: " + e.getMessage());

            // 创建一个错误响应
            MarketListingResponse errorResponse = new MarketListingResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("上架物品异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 变更物品上下架状态
     *
     * @param request 上下架请求
     * @return 上下架响应
     */
    @Override
    public ChangeOnSellStatusRes changeOnSellStatus(ChangeOnSellStatusReq request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 打印请求体，用于调试价格问题
            System.out.println("上下架请求体: " + jsonBody);

            // 发送POST请求
            String endpoint = "web/inventory";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上下架接口响应: " + response);

            // 解析响应
            CommonResult commonResult =
                    gson.fromJson(response, CommonResult.class);

            if (commonResult != null && commonResult.getCode() == 0) {
                System.out.println("IO661上下架操作成功");
                // 从通用响应中提取数据部分
                String dataJson = gson.toJson(commonResult.getData());
                ChangeOnSellStatusRes result = gson.fromJson(dataJson, ChangeOnSellStatusRes.class);

                // 如果是下架操作（价格为null或0），自动下架YouPin对应商品
//                autoUnlistYouPinItems(request);

                return result;
            } else {
                System.out.println("上下架操作失败: " + (commonResult != null ? commonResult.getMsg() : "响应为空"));
                return new ChangeOnSellStatusRes();
            }
        } catch (Exception e) {
            System.out.println("上下架操作异常: " + e.getMessage());
            return new ChangeOnSellStatusRes();
        }
    }

    /**
     * 自动下架YouPin对应商品
     * @param request IO661下架请求
     */
    private void autoUnlistYouPinItems(ChangeOnSellStatusReq request) {
        try {
            if (request == null || request.getInventoryList() == null || request.getInventoryList().isEmpty()) {
                return;
            }

            // 检查是否为下架操作（价格为null或0表示下架）
            boolean isUnlistOperation = request.getInventoryList().stream()
                    .anyMatch(item -> item.getPrice() == null || item.getPrice() == 0);

            if (!isUnlistOperation) {
                System.out.println("不是下架操作，跳过YouPin自动下架");
                return;
            }

            System.out.println("检测到IO661下架操作，开始自动下架YouPin对应商品");

            // 获取所有绑定的Steam账户
            List<String> allSteamIds = getAllBoundSteamIds();
            if (allSteamIds.isEmpty()) {
                System.out.println("未找到绑定的Steam账户，跳过YouPin自动下架");
                return;
            }

            // 提取需要下架的物品（价格为null或0的物品）
            List<ChangeOnSellStatusReq.Inventory> itemsToUnlist = request.getInventoryList().stream()
                    .filter(item -> item.getPrice() == null || item.getPrice() == 0)
                    .collect(Collectors.toList());

            if (itemsToUnlist.isEmpty()) {
                System.out.println("没有需要下架的物品");
                return;
            }

            // 为每个Steam账户尝试下架YouPin商品
            for (String steamId : allSteamIds) {
                unlistYouPinItemsForSteamId(steamId, itemsToUnlist);
            }

        } catch (Exception e) {
            System.err.println("自动下架YouPin商品异常: " + e.getMessage());
        }
    }

    /**
     * 为指定steamId下架YouPin商品
     */
    private void unlistYouPinItemsForSteamId(String steamId, List<ChangeOnSellStatusReq.Inventory> items) {
        try {
            // 获取YouPin token
            String youPinToken = readYouPinCookie(steamId);
            if (youPinToken == null || youPinToken.isEmpty()) {
                System.out.println("未找到steamId=" + steamId + "的YouPin授权令牌，跳过自动下架");
                return;
            }

            // 获取YouPin在售数据
            YouPinUserInventoryOnSellDataListRes onSellData = youPinService.getUserInventoryOnSellDataList(youPinToken);
            if (onSellData == null || onSellData.getData() == null ||
                    onSellData.getData().getCommodityInfoList() == null) {
                System.out.println("获取YouPin在售数据失败，steamId=" + steamId);
                return;
            }

            // 根据assetId找到对应的commodityId
            List<String> commodityIds = new ArrayList<>();
            for (ChangeOnSellStatusReq.Inventory item : items) {
                // 检查assetId是否为null
                if (item.getAssetId() == null) {
                    System.out.println("物品的assetId为null，跳过该物品，物品ID: " + item.getId());
                    continue;
                }

                String commodityId = findCommodityIdByAssetId(
                        String.valueOf(item.getAssetId()),
                        onSellData.getData().getCommodityInfoList()
                );
                if (commodityId != null) {
                    commodityIds.add(commodityId);
                }
            }

            if (commodityIds.isEmpty()) {
                System.out.println("未找到需要下架的YouPin商品，steamId=" + steamId);
                return;
            }

            // 构建YouPin下架请求
            YouPinUserItemsOffSaleReq youPinOffSaleReq = new YouPinUserItemsOffSaleReq();
            List<YouPinUserItemsOffSaleReq.Ids> idsList = commodityIds.stream()
                    .map(commodityId -> {
                        YouPinUserItemsOffSaleReq.Ids ids = new YouPinUserItemsOffSaleReq.Ids();
                        ids.setId(commodityId);
                        return ids;
                    })
                    .collect(Collectors.toList());

            youPinOffSaleReq.setIds(idsList);

            // 执行YouPin下架
            boolean youPinResult = youPinService.userItemsOffSale(youPinToken, youPinOffSaleReq);
            System.out.println("YouPin自动下架结果 (steamId: " + steamId + "): " +
                    (youPinResult ? "成功" : "失败") + ", 商品数量: " + commodityIds.size());

        } catch (Exception e) {
            System.err.println("下架YouPin商品异常 (steamId: " + steamId + "): " + e.getMessage());
        }
    }

    /**
     * 根据AssetId查找对应的CommodityId
     */
    private String findCommodityIdByAssetId(String assetId, List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> commodityInfoList) {
        if (assetId == null || assetId.equals("null")) {
            System.out.println("assetId为null或字符串'null'，无法查找对应的CommodityId");
            return null;
        }

        if (commodityInfoList == null || commodityInfoList.isEmpty()) {
            System.out.println("YouPin商品列表为空，无法查找CommodityId");
            return null;
        }

        System.out.println("正在查找AssetId=" + assetId + "对应的CommodityId，YouPin商品总数: " + commodityInfoList.size());

        for (YouPinUserInventoryOnSellDataListRes.CommodityInfoList commodity : commodityInfoList) {
            // YouPin的steamAssetId与IO661的AssetId是相同的
            String youPinSteamAssetId = commodity.getSteamAssetId();
            if (assetId.equals(youPinSteamAssetId)) {
                System.out.println("找到匹配的商品: AssetId=" + assetId + " -> CommodityId=" + commodity.getId());
                return String.valueOf(commodity.getId());
            }
        }

        System.out.println("未找到AssetId=" + assetId + "对应的CommodityId");
        return null;
    }

    /**
     * 根据assetId获取对应的steamId
     * 由于无法直接确定assetId属于哪个Steam账户，我们返回所有绑定的Steam账户ID
     * 让调用方为所有账户尝试下架操作
     */
    private List<String> getAllBoundSteamIds() {
        try {
            String token = UserCookieManager.readToken();
            if (token == null || token.isEmpty()) {
                System.out.println("未找到授权令牌，无法获取Steam账户列表");
                return new ArrayList<>();
            }

            // 创建AccountManagerService实例来获取Steam账户列表
            AccountManagerService accountManagerService = new com.io661.extension.service.Impl.AccountManagerServiceImpl();
            List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(token);

            if (steamBindList == null || steamBindList.isEmpty()) {
                System.out.println("未找到绑定的Steam账户");
                return new ArrayList<>();
            }

            // 提取所有steamId
            return steamBindList.stream()
                    .map(SteamRes.SteamBind::getSteamId)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            System.err.println("获取Steam账户列表异常: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    // LocalDateTime的TypeAdapter
    private static class LocalDateTimeAdapter extends TypeAdapter<LocalDateTime> {
        private final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public void write(JsonWriter out, LocalDateTime value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(formatter.format(value));
            }
        }

        @Override
        public LocalDateTime read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String dateStr = in.nextString();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            try {
                return LocalDateTime.parse(dateStr, formatter);
            } catch (Exception e) {
                // 尝试其他格式
                try {
                    return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception ex) {
                    System.out.println("解析日期时间失败: " + dateStr);
                    return null;
                }
            }
        }
    }
    /**
     * 获取订单列表
     */
    public IO661TradeOfferRes getAllOrders(IO661TradeOrdersReq request){
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            Gson gson = new Gson();

            long page = request.getPage() == null ? 1L : request.getPage();
            long limit = request.getLimit() == null ? 15L : request.getLimit();
            long type = request.getType() == null? 2L : request.getType();
            long subType = request.getSubType() == null? 0L : request.getSubType();
            String search = request.getSearch() == null? "" : request.getSearch();
            String steamId = request.getSteamId() == null? "" : request.getSteamId();

            // 对搜索参数进行URL编码
            String encodedSearch = java.net.URLEncoder.encode(search, StandardCharsets.UTF_8);
            String encodedSteamId = java.net.URLEncoder.encode(steamId, StandardCharsets.UTF_8);

            // 构建请求URL
            String endpoint = "web/order?page=" + page + "&limit=" + limit + "&type=" + type + "&subType=" + subType;

            // 只有当搜索参数不为空时才添加到URL中
            if (!search.isEmpty()) {
                endpoint += "&search=" + encodedSearch;
            }

            // 只有当steamId不为空时才添加到URL中
            if (!steamId.isEmpty()) {
                endpoint += "&steamId=" + encodedSteamId;
            }

            // 发送GET请求
            String response = httpClient.doGet(endpoint, null, headers);

            // 解析响应
            IO661TradeOfferRes tradeOfferRes = gson.fromJson(response, IO661TradeOfferRes.class);

            if (tradeOfferRes != null && tradeOfferRes.getCode() == 0 && tradeOfferRes.getData() != null) {
                return tradeOfferRes;
            } else {
                System.out.println("获取订单列表失败: " + (tradeOfferRes != null ? tradeOfferRes.getMsg() : "响应为空"));
                return new IO661TradeOfferRes();
            }

        } catch (Exception e) {
            System.err.println("获取订单列表异常: " + e.getMessage());
        }
        return new IO661TradeOfferRes();
    }

    /**
     * 获取单个订单详情
     */
    public IO661OrderStatusRes getOrderStatus(String orderNo){
        IO661OrderStatusRes result = new IO661OrderStatusRes();

        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            Gson gson = new Gson();

            // 发送POST请求
            String endpoint = "web/order/" + orderNo;
            String response = httpClient.doGet(endpoint, null, headers);

            IO661OrderStatusRes orderStatus = gson.fromJson(response, IO661OrderStatusRes.class);
            if (orderStatus!= null && orderStatus.getCode() == 0 && orderStatus.getData()!= null) {
                result.setCode(0);
                result.setMsg(orderStatus.getMsg());
                result.setData(orderStatus.getData());
            }
            return result;

        }catch (Exception e){
            System.out.println(e.getMessage());
        }
        return new IO661OrderStatusRes();
    }



    /**
     * 获取报价列表
     */
    public IO661TradeOfferRes getAllTradeOffers(GetTradingListReq request){
        IO661TradeOfferRes result = new IO661TradeOfferRes();
        String token = UserCookieManager.readToken();

        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            String steamId = request.getSteamId() == null? "" : request.getSteamId();
            long page = request.getPage() == null? 1L : request.getPage();
            long limit = request.getLimit() == null? 15L : request.getLimit();
            String search = request.getSearch() == null? "" : request.getSearch();

            // 发送POST请求
            String endpoint = "app/trade?page=" + page + "&limit=" + limit + "&steamId=" + steamId + "&search=" + search;
            String response = httpClient.doGet(endpoint, null, headers);

            Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter()).create();

            GetTradingListRes data = new Gson().fromJson(response, GetTradingListRes.class);
            String json = gson.toJson(data);

            System.out.println(json);

        }catch (Exception e){
            System.out.println(e.getMessage());
        }

        return result;
    }
}

<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.control.cell.*?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="638.0" prefWidth="832.0" styleClass="withdraw-card" xmlns="http://javafx.com/javafx/21" fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/withdraw-card.css" />
    </stylesheets>

    <VBox layoutX="10.0" layoutY="26.0" prefHeight="626.0" prefWidth="832.0" styleClass="withdraw-container" AnchorPane.bottomAnchor="24.0" AnchorPane.leftAnchor="-1.0" AnchorPane.rightAnchor="1.0" AnchorPane.topAnchor="15.0">
        <padding>
            <Insets bottom="20" left="20" right="20" top="20" />
        </padding>

        <!-- 顶部标题 -->
        <Label layoutX="24.0" prefHeight="37.0" prefWidth="84.0" styleClass="withdraw-title" text="提现"
               AnchorPane.leftAnchor="0" AnchorPane.rightAnchor="0" layoutY="10" />

        <!-- 操作选项卡 -->
        <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="752.0" spacing="10.0">
            <padding>
                <Insets bottom="10" top="10" />
            </padding>
            <children>
                <Button fx:id="withdrawOperationButton" mnemonicParsing="false" styleClass="tab-button" text="提现操作" />
                <Button fx:id="withdrawHistoryButton" mnemonicParsing="false" styleClass="tab-button" text="提现记录" />
            </children>
        </HBox>

        <!-- 内容容器 - 使用StackPane管理多个面板 -->
        <StackPane prefHeight="450.0" prefWidth="752.0" styleClass="content-container">
            <!-- 提现操作面板 - 初始显示 -->
            <AnchorPane fx:id="operationPanel" styleClass="panel" visible="true">
                <VBox layoutX="10.0" layoutY="10.0" prefHeight="430.0" prefWidth="732.0" spacing="15.0">
                    <padding>
                        <Insets bottom="10" left="10" right="10" top="10" />
                    </padding>

                    <!-- 可提现金额显示 -->
                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="info-row">
                        <Label styleClass="available-amount-label" text="可提现：" />
                        <Label fx:id="availableWithdrawAmount" styleClass="available-amount-value" text="¥0.00" />
                    </HBox>

                    <!-- 提现金额输入 -->
                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="info-row">
                        <Label prefWidth="120.0" styleClass="info-label" text="提现金额:" />
                        <TextField fx:id="withdrawAmount" prefWidth="200.0" promptText="请输入提现金额" styleClass="withdraw-input" />
                    </HBox>

                    <!-- 金额计算显示 -->
                    <HBox spacing="30.0" styleClass="amount-row">
                        <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="info-row">
                            <Label prefWidth="80.0" styleClass="info-label" text="实际到账：" />
                            <Label fx:id="actualAmount" styleClass="value-label" text="¥0" />
                        </HBox>
                        <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="info-row">
                            <Label prefWidth="60.0" styleClass="info-label" text="手续费：" />
                            <Label fx:id="feeAmount" styleClass="fee-label" text="¥0" />
                        </HBox>
                    </HBox>

                    <!-- 支付宝账号 -->
                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="info-row">
                        <Label prefWidth="120.0" styleClass="info-label" text="支付宝账号(手机号):" />
                        <TextField fx:id="withdrawAccountDisplay" prefWidth="200.0" styleClass="account-display" text="" />
                    </HBox>

                    <!-- 提现须知 -->
                    <VBox spacing="8.0" styleClass="notice-container">
                        <Label styleClass="notice-title" text="提现须知" />
                        <Label styleClass="notice-item" text="1. 只允许提现到实名认证所绑定的支付宝" />
                        <Label styleClass="notice-item" text="2. 发起提现后预计一个工作日内处理完毕" />
                        <Label styleClass="notice-item" text="3. 每笔提现最低10元，上不封顶" />
                    </VBox>

                    <!-- 确定提现按钮 -->
                    <HBox alignment="CENTER" prefHeight="50.0">
                        <Button fx:id="startWithdrawButton" styleClass="withdraw-button" text="确定提现" />
                    </HBox>
                </VBox>
            </AnchorPane>

            <!-- 提现记录面板 -->
            <AnchorPane fx:id="historyPanel" prefWidth="764.0" styleClass="panel" visible="false">
                <VBox layoutX="10.0" layoutY="10.0" prefHeight="430.0" prefWidth="732.0" spacing="15.0">
                    <padding>
                        <Insets bottom="10" left="10" right="10" top="10" />
                    </padding>

                    <!-- 筛选区域 -->
                    <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="732.0" spacing="15.0">
                        <Label styleClass="filter-label" text="日期:" />
                        <DatePicker fx:id="startDatePicker" prefWidth="130.0" promptText="开始日期" styleClass="date-picker" />
                        <Label styleClass="filter-label" text="至" />
                        <DatePicker fx:id="endDatePicker" prefWidth="130.0" promptText="结束日期" styleClass="date-picker" />
                        <Label styleClass="filter-label" text="状态:" />
                        <ComboBox fx:id="withdrawStatusComboBox" prefWidth="100" promptText="全部" styleClass="status-combo" />
                        <Button fx:id="searchButton" styleClass="search-button" text="查询" />
                        <Button fx:id="resetButton" styleClass="reset-button" text="重置" />
                    </HBox>

                    <!-- 记录表格 -->
                    <TableView fx:id="withdrawRecordTable" prefHeight="317.0" prefWidth="1307.0" styleClass="record-table">
                        <columns>
                            <TableColumn fx:id="recordIdColumn" prefWidth="180.0" text="流水号">
                                <cellValueFactory>
                                    <PropertyValueFactory property="bizNo" />
                                </cellValueFactory>
                            </TableColumn>
                            <TableColumn fx:id="timeColumn" prefWidth="150.0" text="创建时间">
                                <cellValueFactory>
                                    <PropertyValueFactory property="createTime" />
                                </cellValueFactory>
                            </TableColumn>
                            <TableColumn fx:id="statusColumn" prefWidth="80.0" text="状态">
                                <cellValueFactory>
                                    <PropertyValueFactory property="statusText" />
                                </cellValueFactory>
                            </TableColumn>
                            <TableColumn fx:id="amountColumn" prefWidth="100.0" text="提现金额">
                                <cellValueFactory>
                                    <PropertyValueFactory property="amountText" />
                                </cellValueFactory>
                            </TableColumn>
                            <TableColumn fx:id="realAmountColumn" prefWidth="100.0" text="到账金额">
                                <cellValueFactory>
                                    <PropertyValueFactory property="realAmountText" />
                                </cellValueFactory>
                            </TableColumn>
                            <TableColumn fx:id="accountColumn" prefWidth="120.0" text="提现账号">
                                <cellValueFactory>
                                    <PropertyValueFactory property="account" />
                                </cellValueFactory>
                            </TableColumn>
                        </columns>
                    </TableView>

                    <!-- 分页控制 -->
                    <HBox alignment="CENTER" prefHeight="30.0" prefWidth="732.0" spacing="10.0">
                        <Button fx:id="prevPageButton" styleClass="page-button" text="上一页" />
                        <Label fx:id="pageInfoLabel" styleClass="page-info" text="第 1 页，共 1 页" />
                        <Button fx:id="nextPageButton" styleClass="page-button" text="下一页" />
                    </HBox>
                </VBox>
            </AnchorPane>
        </StackPane>
    </VBox>

    <!-- 关闭按钮 -->
    <Button fx:id="closeButtonx" layoutX="780.0" layoutY="3.0" styleClass="close-buttonx" text="×" />
</AnchorPane>

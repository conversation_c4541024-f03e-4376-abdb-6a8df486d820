package com.io661.extension.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.io661.extension.Main;
import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.util.Buff.BuffCookieManager;
import com.io661.extension.util.SQLite.SqliteManager;
import javafx.scene.control.Alert;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.devtools.v136.network.Network;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Stream;

public class WebEngineManager {
    // 定义cookieMap结构（每次调用都重置）
    private static Map<String, Map<String, String>> cookieMap;

    // 定时任务执行器
    private static ScheduledExecutorService scheduler;
    public static String Engine_PATH;

    static {
        // 用户主目录中的数据库路径
        String userHomePath = Paths.get(
                System.getProperty("user.home"),
                ".io661",
                "msedgedriver.exe"
        ).toString();

        // 创建用户目录
        File userDir = new File(Paths.get(System.getProperty("user.home"), ".io661").toString());
        if (!userDir.exists()) {
            userDir.mkdirs();
        }

        // 设置数据库路径为用户目录下的文件
        Engine_PATH = userHomePath;

        // 如果用户目录下没有数据库文件，尝试复制内置数据库或创建新的
        File dbFile = new File(Engine_PATH);
        if (!dbFile.exists()) {
            // 先尝试从开发环境路径复制
            String devDbPath = Paths.get(
                    System.getProperty("user.dir"),
                    "src", "main", "resources", "webEngine", "msedgedriver.exe"
            ).toString();

            if (new File(devDbPath).exists()) {
                try {
                    Files.copy(Paths.get(devDbPath), Paths.get(Engine_PATH));
                    System.out.println("已从开发环境复制数据库到: " + Engine_PATH);
                } catch (IOException e) {
                    System.err.println("从开发环境复制数据库失败: " + e.getMessage());
                }
            } else {
                // 尝试从资源中提取数据库文件
                try (InputStream is = SqliteManager.class.getResourceAsStream("/webEngine/msedgedriver.exe")) {
                    if (is != null) {
                        Files.copy(is, Paths.get(Engine_PATH));
                        System.out.println("已从资源提取数据库到: " + Engine_PATH);
                    } else {
                        System.out.println("未找到资源中的数据库，将创建新数据库: " + Engine_PATH);
                    }
                } catch (IOException e) {
                    System.err.println("从资源提取数据库失败: " + e.getMessage());
                }
            }
        }

    }
    // ================ 新增方法：增强的进程清理 ================
    private static void killEdgeProcesses() {
        try {
            System.out.println("开始清理Edge相关进程...");

            // 添加更多Edge相关进程名
            String[] processes = {
                    "msedge.exe", "msedgedriver.exe", "MicrosoftEdge.exe",
                    "MicrosoftEdgeCP.exe", "edge.exe", "chromedriver.exe",
                    "MicrosoftEdgeUpdate.exe", "msedgewebview2.exe"
            };

            for (String proc : processes) {
                try {
                    Process killProcess = Runtime.getRuntime().exec("taskkill /F /IM " + proc + " /T");
                    killProcess.waitFor(3, TimeUnit.SECONDS); // 等待进程结束
                    System.out.println("尝试结束进程: " + proc);
                } catch (Exception e) {
                    // 忽略单个进程结束失败的异常
                }
            }

            // 额外清理：强制结束所有Edge相关进程
            try {
                Runtime.getRuntime().exec("wmic process where \"name like '%edge%'\" delete").waitFor(3, TimeUnit.SECONDS);
            } catch (Exception e) {
                // 忽略异常
            }

            Thread.sleep(3000); // 等待进程完全结束
            System.out.println("进程清理完成");
        } catch (Exception e) {
            System.err.println("进程清理异常: " + e.getMessage());
        }
    }

    // ================ 新增方法：清理临时目录 ================
    private static void cleanupTempDirectories() {
        try {
            String userTempDir = System.getenv("LOCALAPPDATA") + "\\Temp";
            Path tempDir = Paths.get(userTempDir);

            // 清理所有以edge_selenium_开头的临时目录
            try (Stream<Path> paths = Files.walk(tempDir, 1)) {
                paths.filter(Files::isDirectory)
                     .filter(path -> path.getFileName().toString().startsWith("edge_selenium_"))
                     .forEach(path -> {
                         try {
                             deleteDirectory(path);
                             System.out.println("已清理临时目录: " + path);
                         } catch (Exception e) {
                             System.out.println("清理临时目录失败: " + path + " - " + e.getMessage());
                         }
                     });
            }
        } catch (Exception e) {
            System.err.println("清理临时目录异常: " + e.getMessage());
        }
    }

    // ================ 新增方法：递归删除目录 ================
    private static void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            try (Stream<Path> paths = Files.walk(directory)) {
                paths.sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                     .forEach(path -> {
                         try {
                             Files.deleteIfExists(path);
                         } catch (IOException e) {
                             // 忽略删除失败的文件
                         }
                     });
            }
        }
    }

    /**
     * 获取steamCookie（优化速度版）
     */
    public static String startGetSteamCookies() {
        // 初始化cookieMap结构（每次调用都重置）
        resetCookieMap();

        String result = "";
        EdgeDriver driver = null;
        Path tempUserDataDir = null;

        try {
            // 第一步：彻底清理进程和临时目录
            System.out.println("正在清理Edge进程和临时目录...");
            killEdgeProcesses();
            cleanupTempDirectories();

            // 配置同步系统代理
            System.setProperty("java.net.useSystemProxies", "true");
            System.setProperty("webdriver.edge.driver", Engine_PATH);

            CommonShowAlert.showSyncAlert(Alert.AlertType.INFORMATION,"提示" , "找到驱动引擎地址:" + Engine_PATH);

            // 获取当前用户临时目录（兼容管理员模式）
            String userTempDir = System.getenv("LOCALAPPDATA") + "\\Temp";
            if (userTempDir == null || userTempDir.equals("null\\Temp")) {
                // 备用方案：使用系统临时目录
                userTempDir = System.getProperty("java.io.tmpdir");
            }

            // 创建唯一目录名（增加更多随机性）
            String uniqueDirName = "edge_selenium_" +
                    System.currentTimeMillis() + "_" +
                    UUID.randomUUID().toString().replace("-", "") + "_" +
                    Thread.currentThread().getId();

            tempUserDataDir = Paths.get(userTempDir, uniqueDirName);

            // 确保目录不存在，如果存在则删除
            if (Files.exists(tempUserDataDir)) {
                deleteDirectory(tempUserDataDir);
            }

            Files.createDirectories(tempUserDataDir);
            System.out.println("创建临时用户数据目录: " + tempUserDataDir.toAbsolutePath());

            // 设置Edge选项（增加更多隔离参数）
            EdgeOptions options = new EdgeOptions();
            options.addArguments(
                    "--user-data-dir=" + tempUserDataDir.toAbsolutePath(),
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-default-apps",
                    "--disable-popup-blocking",
                    "--disable-translate",
                    "--disable-background-timer-throttling",
                    "--disable-renderer-backgrounding",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-ipc-flooding-protection",
                    "--delete-all-cookies",
                    "--clear-cache",
                    "--disable-blink-features=AutomationControlled",
                    "--log-level=3",
                    "--new-window", // 确保全新会话
                    "--remote-debugging-port=0" // 使用随机端口
            );
            options.setExperimentalOption("excludeSwitches", Arrays.asList("enable-automation", "enable-logging"));

            Map<String, Object> prefs = new HashMap<>();
            prefs.put("credentials_enable_service", false);
            prefs.put("profile.password_manager_enabled", false);
            prefs.put("profile.default_content_setting_values.notifications", 2);
            options.setExperimentalOption("prefs", prefs);

            driver = new EdgeDriver(options);
            disableSeleniumLogs();
            driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(5));

            // 启用 DevTools 协议
            DevTools devTools = driver.getDevTools();
            devTools.createSession();
            devTools.send(Network.enable(Optional.empty(), Optional.empty(), Optional.empty()));

            // 设置 Network 事件监听器（忽略常见错误）
            devTools.addListener(Network.responseReceived(), responseReceived -> {
                String url = responseReceived.getResponse().getUrl();
                String domain = extractDomain(url);
                if (cookieMap.containsKey(domain)) {
                    try {
                        List<org.openqa.selenium.devtools.v136.network.model.Cookie> cookies = devTools.send(Network.getCookies(Optional.of(Collections.singletonList(url))));
                        processCdpCookies(cookies);
                    } catch (Exception e) {
                        System.out.println("处理CDPCookie失败:" + e);
                    }
                }
            });

            // 修改网络错误监听器，忽略常见错误
            devTools.addListener(Network.loadingFailed(), loadingFailed -> {
                String error = loadingFailed.getErrorText();
                // 忽略常见的无害错误
                if (!error.equals("net::ERR_ABORTED") &&
                        !error.contains("ERR_CONNECTION_CLOSED") &&
                        !error.contains("ERR_EMPTY_RESPONSE")) {
                    System.err.println("网络加载失败: " + error);
                    System.err.println("URL: " + loadingFailed.getRequestId());
                }
            });

            // 加载 Steam 登录页面
            driver.get("https://steamcommunity.com/login/home/<USER>");
            driver.manage().window().maximize();

            // 等待页面加载完成（快速检测）
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
            EdgeDriver finalDriver = driver;
            wait.until(webDriver -> {
                try {
                    return "complete".equals(finalDriver.executeScript("return document.readyState")) ||
                            !finalDriver.getTitle().isEmpty();
                } catch (Exception e) {
                    return false;
                }
            });

            System.out.println("请在浏览器中登录 Steam 账号，系统正在自动收集 cookies...");
            System.out.println("登录成功后请关闭浏览器窗口，程序将自动保存收集到的 cookies");

            // 开始快速收集 cookies
            startFastCookieCollection(driver, devTools);

            // 等待浏览器关闭
            while (true) {
                try {
                    driver.executeScript("return document.readyState");
                    Thread.sleep(1000);
                } catch (Exception e) {
                    System.out.println("浏览器已关闭，退出等待循环");
                    break;
                }
            }

            // 输出最终结果
            result = printResults();

        } catch (Exception e) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "浏览器打开失败", String.valueOf(e));
            System.out.println("浏览器打开失败:" + e);
            e.printStackTrace(); // 打印详细错误信息
        } finally {
            // 关闭定时任务
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdownNow();
            }

            // 确保浏览器关闭
            if (driver != null) {
                try {
                    driver.quit();
                    System.out.println("浏览器已成功关闭");
                } catch (Exception e) {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "浏览器关闭失败", String.valueOf(e));
                    System.out.println("浏览器关闭失败： " + e.getMessage());
                }
            }

            // 再次清理Edge进程（确保完全清理）
            try {
                Thread.sleep(2000); // 等待浏览器完全关闭
                killEdgeProcesses();
            } catch (Exception e) {
                System.out.println("最终进程清理异常: " + e.getMessage());
            }

            // 清理临时用户数据目录
            if (tempUserDataDir != null && Files.exists(tempUserDataDir)) {
                try {
                    deleteDirectory(tempUserDataDir);
                    System.out.println("已清理临时用户数据目录: " + tempUserDataDir);
                } catch (Exception e) {
                    System.out.println("清理临时用户数据目录失败: " + e.getMessage());
                }
            }

            // 清除状态
            cookieMap.clear();
        }

        return result;
    }

    /**
     * 重置cookieMap（确保每次调用都是全新状态）
     */
    private static void resetCookieMap() {
        cookieMap = new LinkedHashMap<>();
        cookieMap.put("api.steampowered", new HashMap<>());
        cookieMap.put("login.steampowered", new HashMap<String, String>() {{
            put("steamRefresh_steam", "");
            put("ak_bmsc", "");
            put("bm_sv", "");
        }});
        cookieMap.put("store.steampowered", new HashMap<String, String>() {{
            put("steamLoginSecure", "");
        }});
        cookieMap.put("steamcommunity", new HashMap<String, String>() {{
            put("steamCountry", "");
            put("steamLoginSecure", "");
            put("sessionid", "");
            put("browserid", "");
            put("webTradeEligibility", "");
            put("tsTradeOffersLastRead", "");
            put("steamDidLoginRefresh", "");
        }});
        cookieMap.put("help.steampowered", new HashMap<String, String>() {{
            put("steamCountry", "");
            put("sessionid", "");
            put("steamLoginSecure", "");
        }});
        cookieMap.put("checkout.steampowered", new HashMap<String, String>() {{
            put("steamLoginSecure", "");
        }});
    }

    /**
     * 启动快速收集 cookies 的任务
     */
    private static void startFastCookieCollection(EdgeDriver driver, DevTools devTools) {
        // 创建定时任务执行器
        scheduler = Executors.newScheduledThreadPool(1);

        // 用于跟踪登录状态
        final boolean[] loginProcessed = {false};
        final String[] extractedSteamId = {null};

        // 每0.5秒执行一次（加快收集速度）
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 获取当前URL
                String currentUrl = driver.getCurrentUrl();

                // 检查是否已登录
                boolean isLoggedIn = (currentUrl.contains("store.steampowered.com") &&
                        !currentUrl.contains("/login")) ||
                        (currentUrl.contains("steamcommunity.com") &&
                                !currentUrl.contains("/login"));

                // 也通过检查store.steampowered的steamLoginSecure cookie来判断是否登录
                if (!isLoggedIn && cookieMap.containsKey("store.steampowered")) {
                    Map<String, String> storeCookies = cookieMap.get("store.steampowered");
                    if (storeCookies.containsKey("steamLoginSecure") &&
                            storeCookies.get("steamLoginSecure") != null &&
                            !storeCookies.get("steamLoginSecure").isEmpty()) {
                        isLoggedIn = true;
                    }
                }

                // 如果检测到已登录但还没有处理
                if (isLoggedIn && !loginProcessed[0]) {
                    loginProcessed[0] = true;
                    System.out.println("检测到登录成功，开始收集关键cookies...");

                    // 只访问关键页面获取必要的cookie
                    if (cookieMap.get("login.steampowered").get("steamRefresh_steam").isEmpty()) {
                        // 直接访问login.steampowered获取steamRefresh_steam cookie
                        driver.get("https://login.steampowered.com/");
                        // 短暂等待确保请求发出
                        Thread.sleep(300);
                    }
                }

                // 检查是否已获取到steamRefresh_steam cookie（并且还没有提取steamId）
                if (loginProcessed[0] && extractedSteamId[0] == null) {
                    // 尝试从login.steampowered域名下获取steamRefresh_steam cookie
                    String steamRefreshCookie = cookieMap.get("login.steampowered").get("steamRefresh_steam");
                    if (steamRefreshCookie != null && !steamRefreshCookie.isEmpty()) {
                        // 从cookie值中提取steamId（前17位）
                        if (steamRefreshCookie.length() >= 17) {
                            extractedSteamId[0] = steamRefreshCookie.substring(0, 17);
                            System.out.println("提取到steamId: " + extractedSteamId[0]);

                            // 快速跳转到个人资料页面（不等待）
                            driver.executeScript(
                                    "window.location.href = 'https://steamcommunity.com/profiles/" +
                                            extractedSteamId[0] + "';"
                            );
                        }
                    }
                }

                // 获取当前页面的 cookies
                collectCookiesFromCurrentPage(driver);

                // 使用 DevTools 获取所有 cookies
                List<org.openqa.selenium.devtools.v136.network.model.Cookie> allCdpCookies = devTools.send(Network.getAllCookies());
                processCdpCookies(allCdpCookies);

                // 尝试使用 JavaScript 获取 document.cookie
                try {
                    String documentCookies = (String) driver.executeScript("return document.cookie;");
                    if (documentCookies != null && !documentCookies.isEmpty()) {
                        processCookieString(documentCookies);
                    }
                } catch (Exception e) {
                    System.out.println("尝试使用 JavaScript 获取 document.cookie失败：" + e);
                }

                // 确保所有 cookie 键都存在，即使值为空
                ensureAllCookieKeysExist();

                // 检查是否收集到所有必要cookie
                if (checkEssentialCookiesCollected()) {
                    System.out.println("必要cookies已收集完成");
                    // 可以提前结束任务（可选）
                }
            } catch (Exception e) {
                // 忽略所有定时任务中的异常
            }
        }, 0, 500, TimeUnit.MILLISECONDS); // 加快轮询速度
    }

    /**
     * 检查是否已收集到所有必要cookie
     */
    private static boolean checkEssentialCookiesCollected() {
        // 检查关键cookie是否已收集
        boolean hasSteamLoginSecure = !cookieMap.get("store.steampowered").get("steamLoginSecure").isEmpty();
        boolean hasSteamRefresh = !cookieMap.get("login.steampowered").get("steamRefresh_steam").isEmpty();
        boolean hasCommunitySession = !cookieMap.get("steamcommunity").get("sessionid").isEmpty();

        return hasSteamLoginSecure && hasSteamRefresh && hasCommunitySession;
    }

    /**
     * 处理 cookie 字符串
     */
    private static void processCookieString(String cookieString) {
        String[] cookiePairs = cookieString.split("; ");
        for (String pair : cookiePairs) {
            String[] parts = pair.split("=", 2);
            if (parts.length == 2) {
                String name = parts[0];
                String value = parts[1];
                // 检查是否是我们需要的 cookie
                for (Map.Entry<String, Map<String, String>> entry : cookieMap.entrySet()) {
                    Map<String, String> cookies = entry.getValue();
                    if (cookies.containsKey(name)) {
                        cookies.put(name, value);
                    }
                }
            }
        }
    }

    /**
     * 确保所有 cookie 键都存在，即使值为空
     */
    private static void ensureAllCookieKeysExist() {
        for (Map.Entry<String, Map<String, String>> domainEntry : cookieMap.entrySet()) {
            Map<String, String> cookies = domainEntry.getValue();
            // 确保所有键都存在
            for (String key : new HashSet<>(cookies.keySet())) {
                cookies.putIfAbsent(key, "");
            }
        }
    }

    /**
     * 处理 CDP cookies
     */
    private static void processCdpCookies(List<org.openqa.selenium.devtools.v136.network.model.Cookie> cookies) {
        cookies.forEach(cookie -> {
            String cookieDomain = cookie.getDomain();
            // 处理域名，移除前导点
            if (cookieDomain != null && cookieDomain.startsWith(".")) {
                cookieDomain = cookieDomain.substring(1);
            }
            // 尝试匹配不同的域名格式
            String[] possibleDomains = null;
            if (cookieDomain != null) {
                possibleDomains = new String[]{
                        cookieDomain,
                        cookieDomain.replace(".com", ""),
                        cookieDomain.replace("www.", "")
                };
            }

            if (possibleDomains != null) {
                for (String domain : possibleDomains) {
                    if (cookieMap.containsKey(domain)) {
                        processMatchedCookie(domain, cookie);
                        return;
                    }
                }
            }

            // 尝试匹配子域名
            for (String domain : cookieMap.keySet()) {
                if (cookieDomain != null && cookieDomain.contains(domain)) {
                    processMatchedCookie(domain, cookie);
                    break;
                }
            }
        });
    }

    /**
     * 处理匹配到的 cookie
     */
    private static void processMatchedCookie(String domain, org.openqa.selenium.devtools.v136.network.model.Cookie cookie) {
        Map<String, String> targetCookies = cookieMap.get(domain);
        if (targetCookies.containsKey(cookie.getName())) {
            targetCookies.put(cookie.getName(), cookie.getValue());
        }
    }

    /**
     * 从当前页面获取指定的 cookies
     */
    private static void collectCookiesFromCurrentPage(EdgeDriver driver) {
        try {
            Set<Cookie> cookies = driver.manage().getCookies();
            for (Cookie cookie : cookies) {
                String domain = Objects.requireNonNull(cookie.getDomain());
                if (domain.startsWith(".")) {
                    domain = domain.substring(1);
                }
                // 尝试匹配不同的域名格式
                String[] possibleDomains = {
                        domain,
                        domain.replace(".com", ""),
                        domain.replace("www.", "")
                };

                for (String mapDomain : possibleDomains) {
                    if (cookieMap.containsKey(mapDomain)) {
                        processSeleniumCookie(mapDomain, cookie);
                        return;
                    }
                }

                // 尝试匹配子域名
                for (String mapDomain : cookieMap.keySet()) {
                    if (domain.contains(mapDomain)) {
                        processSeleniumCookie(mapDomain, cookie);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 处理匹配到的 Selenium cookie
     */
    private static void processSeleniumCookie(String domain, Cookie cookie) {
        Map<String, String> targetCookies = cookieMap.get(domain);
        if (targetCookies.containsKey(cookie.getName())) {
            targetCookies.put(cookie.getName(), cookie.getValue());
        }
    }

    /**
     * 从 URL 中提取域名
     */
    private static String extractDomain(String url) {
        try {
            String fullDomain = url.split("//")[1].split("/")[0];
            // 移除www前缀和.com后缀
            fullDomain = fullDomain.replace("www.", "");
            if (fullDomain.endsWith(".com")) {
                return fullDomain.substring(0, fullDomain.length() - 4);
            }
            return fullDomain;
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * 打印结果
     */
    private static String printResults() {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        ensureAllCookieKeysExist();

        // 过滤结果
        Map<String, Map<String, String>> filteredCookieMap = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, String>> entry : cookieMap.entrySet()) {
            String domain = entry.getKey();
            Map<String, String> cookies = entry.getValue();

            // 跳过api.steampowered
            if (domain.equals("api.steampowered")) continue;

            // 只添加有值的cookie
            Map<String, String> filteredCookies = new LinkedHashMap<>();
            for (Map.Entry<String, String> cookieEntry : cookies.entrySet()) {
                if (cookieEntry.getValue() != null && !cookieEntry.getValue().isEmpty()) {
                    filteredCookies.put(cookieEntry.getKey(), cookieEntry.getValue());
                }
            }

            if (!filteredCookies.isEmpty()) {
                filteredCookieMap.put(domain, filteredCookies);
            }
        }

        // 输出JSON格式
        String jsonOutput = gson.toJson(filteredCookieMap);
        String encodedJson = Base64.getEncoder().encodeToString(jsonOutput.getBytes());
        System.out.println("Base64编码后的JSON:");
        System.out.println(encodedJson);
        System.out.println("原JSON:");
        System.out.println(jsonOutput);

        return encodedJson;
    }

    /**
     * 禁用 Selenium 日志
     */
    private static void disableSeleniumLogs() {
        System.setProperty("webdriver.edge.silentOutput", "true");
        System.setProperty("selenium.loglevel", "OFF");
        System.setProperty("webdriver.chrome.silentOutput", "true");
        System.setProperty("org.openqa.selenium.logging.Level", "OFF");

        Logger.getLogger("org.openqa.selenium").setLevel(Level.OFF);
        Logger.getLogger("io.github.bonigarcia").setLevel(Level.OFF);
        Logger.getLogger("org.apache.http").setLevel(Level.OFF);
        Logger.getLogger("io.netty").setLevel(Level.OFF);
        Logger.getLogger("com.gargoylesoftware.htmlunit").setLevel(Level.OFF);
    }

    /**
     * 获取buff cookie
     *
     * @return session cookie值
     */
    public static String startGetBuffCookies() {
        String sessionCookie = "";
        // 禁用 Selenium 和相关库的日志输出
        disableSeleniumLogs();

        EdgeOptions options = new EdgeOptions();
        options.addArguments(
                "--disable-blink-features=AutomationControlled", // 禁用自动化检测
                "--incognito", // 无痕模式
                "--disable-gpu",
                "--no-sandbox",
                "--log-level=3", // 设置浏览器日志级别为 ERROR
                "--silent"      // 静默模式
        );

        // 禁用自动化和日志
        options.setExperimentalOption("excludeSwitches",
                Arrays.asList("enable-automation", "enable-logging"));

        // 设置基本浏览器首选项
        Map<String, Object> prefs = new HashMap<>();
        prefs.put("credentials_enable_service", false);
        prefs.put("profile.password_manager_enabled", false);
        prefs.put("profile.default_content_setting_values.notifications", 2);
        options.setExperimentalOption("prefs", prefs);

        // 2. 设置 Edge 驱动路径
        System.setProperty("webdriver.edge.driver", "src/main/resources/webEngine/msedgedriver.exe");
        CommonShowAlert.showSyncAlert(Alert.AlertType.INFORMATION, "提示", "请在浏览器中登录 Buff 账号后关闭浏览器");

        // 3. 初始化带 DevTools 的 Edge 浏览器
        EdgeDriver driver = new EdgeDriver(options);
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(5));

        // 启用 DevTools 协议
        DevTools devTools = driver.getDevTools();
        devTools.createSession();

        try {
            driver.get("https://buff.163.com/");
            driver.manage().window().maximize();

            System.out.println("请在浏览器中登录 Buff 账号...");
            System.out.println("登录成功后请关闭浏览器窗口，程序将自动获取session cookie");

            // 在浏览器关闭前获取session cookie
            boolean cookieFound = false;

            // 每秒检查一次浏览器是否还在运行，同时尝试获取cookie
            while (true) {
                try {
                    // 检查浏览器是否还在运行
                    driver.executeScript("return document.readyState");

                    // 尝试获取cookie
                    if (!cookieFound) {
                        try {
                            // 尝试从Selenium获取
                            Set<Cookie> seleniumCookies = driver.manage().getCookies();
                            for (Cookie cookie : seleniumCookies) {
                                if (Objects.requireNonNull(cookie.getDomain()).contains("buff.163.com") && cookie.getName().equals("session")) {
                                    sessionCookie = cookie.getValue();
                                    System.out.println("已获取到session cookie: " + sessionCookie);
                                    // 保存到buff_cookie.properties文件
                                    boolean saved = BuffCookieManager.saveBuffCookie(sessionCookie);
                                    if (saved) {
                                        System.out.println("已成功保存session cookie到buff_cookie.properties文件");
                                    } else {
                                        System.out.println("保存session cookie到buff_cookie.properties文件失败");
                                    }
                                    cookieFound = true;
                                    break;
                                }
                            }

                            // 如果Selenium方法没获取到，尝试从DevTools获取
                            if (!cookieFound) {
                                var allCookies = devTools.send(Network.getAllCookies());
                                for (var cookie : allCookies) {
                                    if (cookie.getDomain().contains("buff.163.com") && cookie.getName().equals("session")) {
                                        sessionCookie = cookie.getValue();
                                        System.out.println("已获取到session cookie: " + sessionCookie);
                                        // 保存到buff_cookie.properties文件
                                        boolean saved = BuffCookieManager.saveBuffCookie(sessionCookie);
                                        if (saved) {
                                            System.out.println("已成功保存session cookie到buff_cookie.properties文件");
                                        } else {
                                            System.out.println("保存session cookie到buff_cookie.properties文件失败");
                                        }
                                        cookieFound = true;
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // 忽略获取cookie时的异常，继续尝试
                        }
                    }

                    // 暂停一秒
                    Thread.sleep(1000);
                } catch (Exception e) {
                    // 浏览器已关闭，退出循环
                    break;
                }
            }

            // 如果浏览器关闭时还没有找到cookie，再尝试一次
            if (!cookieFound) {
                try {
                    Set<Cookie> seleniumCookies = driver.manage().getCookies();
                    for (Cookie cookie : seleniumCookies) {
                        if (Objects.requireNonNull(cookie.getDomain()).contains("buff.163.com") && cookie.getName().equals("session")) {
                            sessionCookie = cookie.getValue();
                            System.out.println("已获取到session cookie: " + sessionCookie);
                            // 保存到buff_cookie.properties文件
                            boolean saved = BuffCookieManager.saveBuffCookie(sessionCookie);
                            if (saved) {
                                System.out.println("已成功保存session cookie到buff_cookie.properties文件");
                            } else {
                                System.out.println("保存session cookie到buff_cookie.properties文件失败");
                            }
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
            }

        } catch (Exception ignored) {
        } finally {
            driver.quit(); // 关闭浏览器
        }

        return sessionCookie;
    }
}

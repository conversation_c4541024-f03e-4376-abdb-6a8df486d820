package com.io661.extension.controller;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.model.Steam.IO661OrderStatusRes;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import lombok.Data;
import lombok.Setter;

import java.net.URL;
import java.text.DecimalFormat;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

@Data
public class OrderDetailDialogController implements Initializable {

    // FXML 控件
    @FXML private Button refreshDetailButton;
    @FXML private Button confirmButton;

    // 订单信息
    @FXML private Label orderNoLabel;
    @FXML private Label orderTypeLabel;
    @FXML private Label orderStatusLabel;
    @FXML private Label createTimeLabel;
    @FXML private Label updateTimeLabel;

    // 价格信息
    @FXML private Label priceLabel;
    @FXML private Label terminalPriceLabel;
    @FXML private Label feeLabel;

    // 物品信息
    @FXML private ImageView itemImageView;
    @FXML private Label itemNameLabel;
    @FXML private Label hashNameLabel;
    @FXML private Label floatValueLabel;
    @FXML private Label qualityLabel;
    @FXML private Label rarityLabel;
    @FXML private Label exteriorLabel;

    // 买家信息
    @FXML private ImageView buyerAvatarImageView;
    @FXML private Label buyerNicknameLabel;
    @FXML private Label buyerRegDateLabel;

    // 卖家信息
    @FXML private ImageView sellerAvatarImageView;
    @FXML private Label sellerNicknameLabel;
    @FXML private Label sellerRegDateLabel;

    // 进度条容器
    @FXML private HBox progressContainer;

    // 服务和数据
    private TransactionAssistantServiceImpl transactionAssistantService;
    @Setter
    private String orderNo;
    @Setter
    private Stage stage;
    private IO661OrderStatusRes.Data_ orderData;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 初始化服务
        transactionAssistantService = new TransactionAssistantServiceImpl();

        // 设置事件处理
        setupEventHandlers();
    }

    private void setupEventHandlers() {
        // 确定按钮事件
        confirmButton.setOnAction(event -> closeDialog());

        // 刷新详情按钮事件
        refreshDetailButton.setOnAction(event -> loadOrderDetail());
    }

    public void loadOrderDetail() {
        if (orderNo == null || orderNo.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "警告", "订单号不能为空");
            return;
        }

        // 禁用刷新按钮，防止重复请求
        refreshDetailButton.setDisable(true);

        CompletableFuture.supplyAsync(() -> {
            try {
                return transactionAssistantService.getOrderStatus(orderNo);
            } catch (Exception e) {
                System.err.println("获取订单详情失败: " + e.getMessage());
                return new IO661OrderStatusRes();
            }
        }).thenAccept(response -> {
            Platform.runLater(() -> {
                refreshDetailButton.setDisable(false);

                if (response != null && response.getCode() == 0 && response.getData() != null) {
                    orderData = response.getData();
                    updateUI();
                } else {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "错误", "获取订单详情失败: " +
                            (response != null ? response.getMsg() : "未知错误"));
                }
            });
        });
    }

    private void updateUI() {
        if (orderData == null) return;

        DecimalFormat df = new DecimalFormat("#0.00");

        // 更新订单基本信息
        orderNoLabel.setText(orderData.getOrderNo() != null ? orderData.getOrderNo() : "未知");
        orderTypeLabel.setText(getOrderTypeText(orderData.getType()));
        orderStatusLabel.setText(orderData.getStatusDes() != null ? orderData.getStatusDes() : "未知状态");
        createTimeLabel.setText(orderData.getCreateTime() != null ? orderData.getCreateTime() : "未知");
        updateTimeLabel.setText(orderData.getUpdateTime() != null ? orderData.getUpdateTime() : "未知");

        // 设置状态标签样式
        orderStatusLabel.getStyleClass().removeAll("status-waiting", "status-pending", "status-confirming",
                "status-success", "status-failed", "status-unknown");
        orderStatusLabel.getStyleClass().add(getStatusStyleClass(orderData.getStatus()));

        // 更新进度条
        updateProgressBar();

        // 更新价格信息 - 将分转换为元
        priceLabel.setText("¥" + df.format(orderData.getPrice() / 100.0));
        terminalPriceLabel.setText("¥" + df.format(orderData.getTerminalPrice() / 100.0));
        feeLabel.setText("¥" + df.format(orderData.getFee() / 100.0));

        // 更新物品信息
        if (orderData.getTemplateInfo() != null) {
            IO661OrderStatusRes.TemplateInfo templateInfo = orderData.getTemplateInfo();

            itemNameLabel.setText(templateInfo.getItemName() != null ? templateInfo.getItemName() : "未知物品");
            hashNameLabel.setText(templateInfo.getHashName() != null ? templateInfo.getHashName() : "");

            // 设置物品图片
            if (templateInfo.getIconUrl() != null && !templateInfo.getIconUrl().isEmpty()) {
                try {
                    itemImageView.setImage(new Image(templateInfo.getIconUrl(), true));
                } catch (Exception e) {
                    itemImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
                }
            } else {
                itemImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
            }

            // 设置品质、稀有度、磨损标签
            qualityLabel.setText(getQualityText(templateInfo.getTagQuality()));
            rarityLabel.setText(getRarityText(templateInfo.getTagRarity()));
            exteriorLabel.setText(getExteriorText(templateInfo.getTagExterior()));
        }

        // 设置磨损值
        if (orderData.getFloatValue() > 0) {
            floatValueLabel.setText(String.format("%.6f", orderData.getFloatValue()));
        } else {
            floatValueLabel.setText("无");
        }

        // 更新买家信息
        if (orderData.getBuyerInfo() != null) {
            IO661OrderStatusRes.UserInfo buyerInfo = orderData.getBuyerInfo();

            buyerNicknameLabel.setText(buyerInfo.getNickname() != null ? buyerInfo.getNickname() : "未知用户");
            buyerRegDateLabel.setText(buyerInfo.getRegDate() != null ? buyerInfo.getRegDate() : "未知");

            // 设置买家头像
            if (buyerInfo.getAvatar() != null && !buyerInfo.getAvatar().isEmpty()) {
                try {
                    buyerAvatarImageView.setImage(new Image(buyerInfo.getAvatar(), true));
                } catch (Exception e) {
                    buyerAvatarImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
                }
            } else {
                buyerAvatarImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
            }
        }

        // 更新卖家信息
        if (orderData.getSellerInfo() != null) {
            IO661OrderStatusRes.UserInfo sellerInfo = orderData.getSellerInfo();

            sellerNicknameLabel.setText(sellerInfo.getNickname() != null ? sellerInfo.getNickname() : "未知用户");
            sellerRegDateLabel.setText(sellerInfo.getRegDate() != null ? sellerInfo.getRegDate() : "未知");

            // 设置卖家头像
            if (sellerInfo.getAvatar() != null && !sellerInfo.getAvatar().isEmpty()) {
                try {
                    sellerAvatarImageView.setImage(new Image(sellerInfo.getAvatar(), true));
                } catch (Exception e) {
                    sellerAvatarImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
                }
            } else {
                sellerAvatarImageView.setImage(new Image("/com/io661/extension/img/Logo.png"));
            }
        }
    }

    private String getOrderTypeText(int type) {
        switch (type) {
            case 1: return "购买订单";
            case 2: return "出售订单";
            default: return "未知类型";
        }
    }

    private String getStatusStyleClass(int status) {
        switch (status) {
            case 11: return "status-waiting";    // 订单创建中
            case 21: return "status-pending";    // 等待发起报价
            case 31: return "status-confirming"; // 交易搁置
            case 41: return "status-confirming"; // 等待确认报价
            case 51: return "status-success";    // 交易成功
            case 55: return "status-failed";     // 交易失败
            default: return "status-unknown";
        }
    }

    private String getQualityText(String tagQuality) {
        if (tagQuality == null) return "";
        switch (tagQuality.toLowerCase()) {
            case "normal": return "普通";
            case "genuine": return "正品";
            case "vintage": return "复古";
            case "unusual": return "不寻常";
            case "unique": return "独特";
            case "strange": return "奇异";
            default: return tagQuality;
        }
    }

    private String getRarityText(String tagRarity) {
        if (tagRarity == null) return "";
        switch (tagRarity.toLowerCase()) {
            case "rarity_common":
            case "rarity_common_weapon": return "消费级";
            case "rarity_uncommon":
            case "rarity_uncommon_weapon": return "工业级";
            case "rarity_rare":
            case "rarity_rare_weapon": return "军规级";
            case "rarity_mythical":
            case "rarity_mythical_weapon": return "受限";
            case "rarity_legendary":
            case "rarity_legendary_weapon": return "保密";
            case "rarity_ancient":
            case "rarity_ancient_weapon": return "隐秘";
            default: return tagRarity;
        }
    }

    private String getExteriorText(String tagExterior) {
        if (tagExterior == null) return "";
        switch (tagExterior.toLowerCase()) {
            case "wearcategory0": return "崭新出厂";
            case "wearcategory1": return "略有磨损";
            case "wearcategory2": return "久经沙场";
            case "wearcategory3": return "破损不堪";
            case "wearcategory4": return "战痕累累";
            default: return tagExterior;
        }
    }

    private void updateProgressBar() {
        if (progressContainer == null || orderData == null) return;

        // 清空现有内容
        progressContainer.getChildren().clear();

        // 定义进度步骤
        String[] steps = {"下单", "支付", "发货", "收货", "完成"};
        String[] stepTimes = {
                orderData.getCreateTime(),  // 下单时间
                orderData.getCreateTime(),  // 支付时间（假设与下单时间相同）
                "",  // 发货时间（根据状态判断）
                "",  // 收货时间（根据状态判断）
                (orderData.getStatus() == 51 || orderData.getStatus() == 55) ? orderData.getUpdateTime() : ""  // 完成时间
        };

        // 根据订单状态确定当前进度
        int currentStep = getCurrentStep(orderData.getStatus());
        boolean isFailedOrder = orderData.getStatus() == 55; // 交易失败

        for (int i = 0; i < steps.length; i++) {
            // 创建步骤容器
            VBox stepContainer = new VBox(5);
            stepContainer.setAlignment(Pos.CENTER);
            stepContainer.setPrefWidth(100);

            // 创建圆形指示器
            Label indicator = new Label();
            indicator.setPrefSize(30, 30);
            indicator.setAlignment(Pos.CENTER);
            indicator.getStyleClass().add("progress-indicator");

            if (isFailedOrder && i == steps.length - 1) {
                // 失败订单的最后一步显示为失败
                indicator.setText("✗");
                indicator.getStyleClass().add("progress-failed");
            } else if (i < currentStep) {
                // 已完成的步骤
                indicator.setText("✓");
                if (isFailedOrder) {
                    indicator.getStyleClass().add("progress-failed");
                } else {
                    indicator.getStyleClass().add("progress-completed");
                }
            } else if (i == currentStep && !isFailedOrder) {
                // 当前步骤（非失败订单）
                indicator.setText(String.valueOf(i + 1));
                indicator.getStyleClass().add("progress-current");
            } else {
                // 未完成的步骤
                indicator.setText(String.valueOf(i + 1));
                indicator.getStyleClass().add("progress-pending");
            }

            // 创建步骤标签
            Label stepLabel = new Label(steps[i]);
            stepLabel.getStyleClass().add("progress-step-label");

            // 创建时间标签
            Label timeLabel = new Label(stepTimes[i] != null && !stepTimes[i].isEmpty() ? stepTimes[i] : "");
            timeLabel.getStyleClass().add("progress-time-label");

            stepContainer.getChildren().addAll(indicator, stepLabel, timeLabel);

            // 添加到容器
            progressContainer.getChildren().add(stepContainer);

            // 添加连接线（除了最后一个步骤）
            if (i < steps.length - 1) {
                Region line = new Region();
                line.setPrefHeight(2);
                line.getStyleClass().add("progress-line");
                if (i < currentStep - 1) {
                    line.getStyleClass().add("progress-line-completed");
                } else {
                    line.getStyleClass().add("progress-line-pending");
                }
                HBox.setHgrow(line, Priority.ALWAYS);
                progressContainer.getChildren().add(line);
            }
        }
    }

    private int getCurrentStep(int status) {
        // 根据订单状态返回当前进度步骤
        switch (status) {
            case 11: return 1; // 订单创建中 - 已下单
            case 21: return 2; // 等待发起报价 - 已支付
            case 31: return 3; // 交易搁置 - 已发货
            case 41: return 4; // 等待确认报价 - 已收货
            case 51: return 5; // 交易成功 - 已完成
            case 55: return 5; // 交易失败 - 显示为完成状态（但用失败颜色标识）
            default: return 1; // 默认显示第一步
        }
    }

    private void closeDialog() {
        if (stage != null) {
            stage.close();
        }
    }

}

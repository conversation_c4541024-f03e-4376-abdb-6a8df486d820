package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.controller.SteamController;
import com.io661.extension.model.Steam.BindSteamRes;
import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.model.Steam.SteamTokenRes;
import com.io661.extension.service.AccountManagerService;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账号管理方法实现
 */
@Data
public class AccountManagerServiceImpl implements AccountManagerService {
    private final CommonHttpUrl httpClient;
    public AccountManagerServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }

    /**
     * 新增steam账号
     * @param maFile 绑定文件
     */
    @Override
    public void addSteamAccountByMaFile(String maFile) {

    }

    /**
     * 获取所有的steam账号
     */
    @Override
    public List<SteamRes.SteamBind> getAllSteamAccount(String token) {
        try {
            httpClient.setAuthToken(token);
            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            String endpoint = "web/steam";

            String response = httpClient.doGet(endpoint, null, headers);

            Gson gson = new Gson();
            SteamRes steamRes = gson.fromJson(response, SteamRes.class);

            if (steamRes != null && steamRes.getCode() == 0 && steamRes.getData() != null && steamRes.getData().getSteamBindList() != null) {
                List<SteamRes.SteamBind> steamBindList = steamRes.getData().getSteamBindList();
                System.out.println("成功解析Steam账号数据，共 " + steamBindList.size() + " 个账号");
                return steamBindList;
            } else {
                String errorMsg = steamRes != null ? "错误码: " + steamRes.getCode() + ", 消息: " + steamRes.getMsg() : "响应为空";
                System.out.println("获取Steam账号列表失败: " + errorMsg);
                return new ArrayList<>();
            }
        } catch (Exception e) {
            System.out.println("获取Steam账号异常: " + e.getMessage());
        }
        return new ArrayList<>();
    }


    /**
     * 获取Steam账号的令牌
     * @param steamId Steam账号ID
     * @param token 授权令牌
     * @return Steam令牌响应
     */
    @Override
    public SteamTokenRes getSteamToken(String steamId, String token) {
        try {
            httpClient.setAuthToken(token);
            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);

            // 构建请求URL
            String endpoint = "web/steam/" + steamId;

            // 发送GET请求获取令牌
            String response = httpClient.doGet(endpoint, null, headers);
            System.out.println("获取Steam令牌API响应: " + response);

            // 解析响应
            Gson gson = new Gson();
            SteamTokenRes tokenRes = gson.fromJson(response, SteamTokenRes.class);

            if (tokenRes != null && tokenRes.getCode() == 0) {
                System.out.println("获取Steam令牌成功");
                return tokenRes;
            } else {
                String errorMsg = tokenRes != null ? "错误码: " + tokenRes.getCode() + ", 消息: " + tokenRes.getMsg() : "响应为空";
                System.out.println("获取Steam令牌失败: " + errorMsg);
                return null;
            }
        } catch (Exception e) {
            System.out.println("获取Steam令牌异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 绑定Steam令牌（通过.maFile文件）
     * @param maFileContent .maFile文件内容
     * @param password 密码
     * @param token 授权令牌
     * @return 是否绑定成功
     */
    @Override
    public boolean bindSteamTokenByMaFile(String maFileContent, String password, String token) {
        try {
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);

            // 打印请求头以便调试
            System.out.println("请求头: " + headers);

            // 解析.maFile内容
            Gson gson = new Gson();
            JsonObject maFileJson = gson.fromJson(maFileContent, JsonObject.class);

            // 提取所需字段
            String accountName = "";
            if (maFileJson.has("account_name")) {
                accountName = maFileJson.get("account_name").getAsString();
            }

            // 如果account_name不存在，尝试使用steamid
            if ((accountName == null || accountName.isEmpty()) && maFileJson.has("steamid")) {
                accountName = maFileJson.get("steamid").getAsString();
            }

            // 获取token_code (使用shared_secret)
            String tokenCode = "";
            if (maFileJson.has("shared_secret")) {
                tokenCode = maFileJson.get("shared_secret").getAsString();
            }

            // 获取device_id
            String deviceId = "";
            if (maFileJson.has("device_id")) {
                deviceId = maFileJson.get("device_id").getAsString();
            }

            // 获取identity_secret
            String identitySecret = "";
            if (maFileJson.has("identity_secret")) {
                identitySecret = maFileJson.get("identity_secret").getAsString();
            }

            // 打印提取的字段
            System.out.println("从.maFile提取的字段:");
            System.out.println("accountName: " + accountName);
            System.out.println("tokenCode (shared_secret): " + tokenCode);
            System.out.println("deviceId: " + deviceId);
            System.out.println("identitySecret: " + identitySecret);

            // 构建请求体
            String jsonBody = String.format(
                    "{\"username\":\"%s\",\"password\":\"%s\",\"tokenCode\":\"%s\",\"deviceId\":\"%s\",\"identitySecret\":\"%s\"}",
                    accountName, password, tokenCode, deviceId, identitySecret
            );

            // 打印请求体以便调试
            System.out.println("请求体: " + jsonBody);

            // 发送POST请求到Steam令牌绑定接口
            String endpoint = "web/steam";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("Steam令牌绑定API请求: " + endpoint);
            System.out.println("Steam令牌绑定API响应: " + response);

            // 解析响应
            SteamRes steamRes = gson.fromJson(response, SteamRes.class);

            if (steamRes != null && steamRes.getCode() == 0) {
                System.out.println("Steam令牌绑定成功");
                return true;
            } else {
                String errorMsg = steamRes != null ? "错误码: " + steamRes.getCode() + ", 消息: " + steamRes.getMsg() : "响应为空";
                System.out.println("Steam令牌绑定失败: " + errorMsg);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Steam令牌绑定异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 解绑Steam令牌
     * @param steamId Steam账号ID
     * @param token 授权令牌
     * @return 是否解绑成功
     */
    @Override
    public boolean unbindSteamToken(String steamId, String token) {
        try {
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);

            // 发送DELETE请求到Steam令牌解绑接口
            String endpoint = "web/steam/" + steamId;
            String response = httpClient.doDelete(endpoint, headers);

            System.out.println("Steam令牌解绑API请求: " + endpoint);
            System.out.println("Steam令牌解绑API响应: " + response);

            // 解析响应
            Gson gson = new Gson();
            SteamRes steamRes = gson.fromJson(response, SteamRes.class);

            if (steamRes != null && steamRes.getCode() == 0) {
                System.out.println("Steam令牌解绑成功");
                return true;
            } else {
                String errorMsg = steamRes != null ? "错误码: " + steamRes.getCode() + ", 消息: " + steamRes.getMsg() : "响应为空";
                System.out.println("Steam令牌解绑失败: " + errorMsg);
                return false;
            }
        } catch (Exception e) {
            System.out.println("Steam令牌解绑异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 用户在浏览器登陆后用获取cookie后用base64加密后
     * @param cookies 授权令牌
     * @return 是否登录成功
     */
    @Override
    public BindSteamRes loginSteamAccount(String cookies) {
        BindSteamRes result = new BindSteamRes();

        String token = SteamController.getAuthToken();

        try {
            httpClient.setAuthToken(token);
            // 创建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);

            // 打印请求头以便调试
            System.out.println("请求头: " + headers);

            String jsonBody = String.format(cookies);

            // 打印请求体以便调试
            System.out.println("请求体: " + jsonBody);

            // 发送POST请求到Steam账号登录接口
            String endpoint = "app/steam";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("Steam账号登录API请求: " + endpoint);
            System.out.println("Steam账号登录API响应: " + response);

            // 解析响应
            Gson gson = new Gson();
            BindSteamRes steamRes = gson.fromJson(response, BindSteamRes.class);

            if (steamRes != null && steamRes.getCode() == 0) {
                result.setCode(0);
                result.setMsg(steamRes.getMsg());

                return result;
            } else {
                if (steamRes != null) {
                    result.setCode(steamRes.getCode());
                }
                if (steamRes != null) {
                    result.setMsg(steamRes.getMsg());
                }
                return result;
            }
        } catch (Exception e) {
            System.out.println("Steam账号登录异常: " + e.getMessage());
            return new BindSteamRes();
        }
    }
}

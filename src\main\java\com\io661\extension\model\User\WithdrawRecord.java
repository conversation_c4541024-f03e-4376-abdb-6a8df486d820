package com.io661.extension.model.User;

import lombok.Data;

/**
 * 提现记录数据模型
 */
@Data
public class WithdrawRecord {
    /**
     * 商户提现号
     */
    private String bizNo;

    /**
     * 提现金额（分）
     */
    private Integer amount;

    /**
     * 到账金额（分）
     */
    private Integer realAmount;

    /**
     * 提现账号
     */
    private String account;

    /**
     * 完成状态
     */
    private Boolean success;

    /**
     * 创建时间
     */
    private String createTime;

    // 用于表格显示的格式化字段

    /**
     * 格式化的提现金额
     */
    public String getAmountText() {
        if (amount == null) return "¥0.00";
        return String.format("¥%.2f", amount / 100.0);
    }

    /**
     * 格式化的到账金额
     */
    public String getRealAmountText() {
        if (realAmount == null) return "¥0.00";
        return String.format("¥%.2f", realAmount / 100.0);
    }

    /**
     * 状态文本
     */
    public String getStatusText() {
        if (success == null) return "处理中";
        return success ? "成功" : "失败";
    }

    /**
     * 获取状态样式类
     */
    public String getStatusStyleClass() {
        if (success == null) return "status-processing";
        return success ? "status-success" : "status-failed";
    }
}

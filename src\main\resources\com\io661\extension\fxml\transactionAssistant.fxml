<?xml version="1.0" encoding="UTF-8"?>


<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="722.0" prefWidth="1268.0" styleClass="main-container" stylesheets="@../css/transactionAssistant-new.css" xmlns="http://javafx.com/javafx/21" fx:controller="com.io661.extension.controller.TransactionAssistantController">
    <left>
        <VBox prefHeight="722.0" prefWidth="230.0" styleClass="user-info-container">
            <children>
                <HBox alignment="CENTER_LEFT" spacing="10">
                    <Label styleClass="user-info-header" text="本地账号管理" />
                    <Button mnemonicParsing="false" styleClass="card-icon-button">
                        <graphic>
                            <ImageView fitHeight="16.0" fitWidth="16.0">
                                <Image url="@../img/refresh.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                </HBox>

                <!-- 搜索框 -->
                <HBox alignment="CENTER" spacing="5" styleClass="search-box">
                    <padding>
                        <Insets bottom="5" left="5" right="5" top="5" />
                    </padding>
                    <TextField prefWidth="150" promptText="请输入Steam..." styleClass="search-field" />
                    <Button mnemonicParsing="false" styleClass="icon-button">
                        <graphic>
                            <ImageView fitHeight="16.0" fitWidth="16.0">
                                <Image url="@../img/search.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                    <Region HBox.hgrow="ALWAYS" />
                    <Button mnemonicParsing="false" styleClass="icon-button">
                        <graphic>
                            <ImageView fitHeight="16.0" fitWidth="16.0">
                                <Image url="@../img/settings.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                    <Button mnemonicParsing="false" styleClass="icon-button">
                        <graphic>
                            <ImageView fitHeight="16.0" fitWidth="16.0">
                                <Image url="@../img/add.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                </HBox>

                <!-- 账号分组标题 -->
                <HBox alignment="CENTER_LEFT" spacing="5" styleClass="account-group-header">
                    <padding>
                        <Insets bottom="5" left="5" right="5" top="10" />
                    </padding>
                    <Button mnemonicParsing="false" styleClass="expand-button">
                        <graphic>
                            <ImageView fitHeight="12.0" fitWidth="12.0">
                                <Image url="@../img/expand.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                    <Label styleClass="group-label" text="账号分组" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Label fx:id="accountSelectorLabel" styleClass="account-selector-label" text="全部" />
                    <Button mnemonicParsing="false" styleClass="refresh-button">
                        <graphic>
                            <ImageView fitHeight="12.0" fitWidth="12.0">
                                <Image url="@../img/refresh.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                </HBox>

                <!-- 账号列表 -->
                <ScrollPane fitToWidth="true" styleClass="transparent-scroll-pane" VBox.vgrow="ALWAYS">
                    <content>
                        <VBox fx:id="accountListContainer" spacing="5">
                            <!-- 账号列表将在控制器中动态添加 -->
                        </VBox>
                    </content>
                </ScrollPane>
            </children>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
        </VBox>
    </left>
    <center>
        <BorderPane prefHeight="200.0" prefWidth="200.0" BorderPane.alignment="CENTER">
            <top>
                <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="200.0" spacing="0" styleClass="tab-container" BorderPane.alignment="CENTER">
                    <children>
                        <Button fx:id="inventoryButton" mnemonicParsing="false" styleClass="tab-button, active-tab" text="库存" />
                        <Button fx:id="onSaleButton" mnemonicParsing="false" styleClass="tab-button" text="在售" />
                        <Button fx:id="priceButton" mnemonicParsing="false" styleClass="tab-button" text="报价" />
                        <Button fx:id="ordersButton" mnemonicParsing="false" styleClass="tab-button" text="订单" />
                        <Button fx:id="detailButton" mnemonicParsing="false" styleClass="tab-button" text="流水" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" styleClass="window-control-button">
                            <graphic>
                                <ImageView fitHeight="12.0" fitWidth="12.0">
                                    <Image url="@../img/minimize.png" />
                                </ImageView>
                            </graphic>
                        </Button>
                        <Button mnemonicParsing="false" styleClass="window-control-button">
                            <graphic>
                                <ImageView fitHeight="12.0" fitWidth="12.0">
                                    <Image url="@../img/maximize.png" />
                                </ImageView>
                            </graphic>
                        </Button>
                        <Button mnemonicParsing="false" styleClass="window-control-button, close-button">
                            <graphic>
                                <ImageView fitHeight="12.0" fitWidth="12.0">
                                    <Image url="@../img/close.png" />
                                </ImageView>
                            </graphic>
                        </Button>
                    </children>
                </HBox>
            </top>
            <center>
                <StackPane prefHeight="150.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                    <children>
                        <!-- 库存面板 -->
                        <StackPane fx:id="inventoryPane" prefHeight="150.0" prefWidth="200.0">
                            <children>
                                <BorderPane prefHeight="200.0" prefWidth="200.0">
                                    <top>
                                        <VBox BorderPane.alignment="CENTER">
                                            <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="200.0" spacing="10.0" styleClass="filter-container">
                                                <children>
                                                    <CheckBox fx:id="steamCheckbox" mnemonicParsing="false" selected="true" styleClass="platform-checkbox" text="Steam" />
                                                    <CheckBox fx:id="selectAllCheckbox" mnemonicParsing="false" styleClass="filter-checkbox" text="全选" />
                                                    <Region HBox.hgrow="ALWAYS" />
                                                    <Button fx:id="mergeItemsButton" mnemonicParsing="false" styleClass="filter-button" text="合并" />
                                                    <Button fx:id="unmergeItemsButton" mnemonicParsing="false" styleClass="filter-button" text="拆分" />
                                                    <Button fx:id="uploadToMarketButton" mnemonicParsing="false" styleClass="filter-button" text="上架市场" />
                                                </children>
                                                <padding>
                                                    <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
                                                </padding>
                                            </HBox>
                                            <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="200.0" spacing="10.0" styleClass="search-container">
                                                <children>

                                                    <ImageView fitHeight="12.0" fitWidth="12.0">
                                                        <Image url="@../img/search.png" />
                                                    </ImageView>

                                                    <TextField fx:id="searchField" promptText="请输入物品名称..." styleClass="search-field" HBox.hgrow="ALWAYS" />
                                                    <Label styleClass="filter-label" text="排序" />
                                                    <ComboBox fx:id="inventorySortSelector" prefWidth="120" promptText="默认排序" />
                                                    <Label styleClass="filter-label" text="筛选" />
                                                    <ComboBox fx:id="inventoryStatusSelector" prefWidth="120" promptText="默认排序" />
                                                </children>
                                                <padding>
                                                    <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
                                                </padding>
                                            </HBox>
                                            <HBox alignment="CENTER_LEFT" prefHeight="30.0" prefWidth="200.0" spacing="10.0" styleClass="inventory-info">
                                                <children>
                                                    <Label fx:id="inventorySummaryLabel" styleClass="inventory-summary" text="共 0 件物品 总值 ¥ 0.00 上次刷新时间: --" />
                                                    <Region HBox.hgrow="ALWAYS" />
                                                    <Button fx:id="refreshInventoryButton" onAction="#refreshInventoryOnAction" mnemonicParsing="false" styleClass="refresh-button" text="刷新库存" />
                                                </children>
                                                <padding>
                                                    <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
                                                </padding>
                                            </HBox>
                                        </VBox>
                                    </top>
                                    <center>
                                        <ScrollPane fx:id="inventoryScrollPane" fitToWidth="true" prefHeight="200.0" prefWidth="200.0" styleClass="scroll-pane" BorderPane.alignment="CENTER">
                                            <content>
                                                <FlowPane fx:id="inventoryItemsContainer" hgap="10.0" prefWidth="1000.0" vgap="10.0">
                                                    <padding>
                                                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                                    </padding>
                                                </FlowPane>
                                            </content>
                                        </ScrollPane>
                                    </center>
                                </BorderPane>
                            </children>
                        </StackPane>

                        <!-- 在售面板 -->
                        <StackPane fx:id="onSalePane" prefHeight="150.0" prefWidth="200.0" visible="false">
                            <children>
                                <BorderPane prefHeight="200.0" prefWidth="200.0">
                                    <top>
                                        <VBox BorderPane.alignment="CENTER">
                                            <HBox alignment="CENTER_LEFT" prefHeight="40.0" prefWidth="200.0" spacing="10.0" styleClass="filter-container">
                                                <children>
                                                    <Label styleClass="filter-label" text="平台筛选" />
                                                    <ComboBox fx:id="onSellPlateSelector" prefWidth="120" promptText="全部" />
                                                    <Label styleClass="filter-label" text="游戏物品" />
                                                    <TextField fx:id="onSaleSearchField" promptText="搜索" styleClass="search-field" HBox.hgrow="ALWAYS" />
                                                    <Label styleClass="filter-label" text="排序" />
                                                    <ComboBox fx:id="onSaleSortSelector" prefWidth="120" promptText="默认排序" />
                                                    <CheckBox fx:id="onSaleSelectAllCheckbox" mnemonicParsing="false" styleClass="filter-checkbox" text="全选" />
                                                    <Button fx:id="unlistButton" mnemonicParsing="false" styleClass="filter-button" text="下架" />
                                                    <Button fx:id="modifyPriceButton" mnemonicParsing="false" styleClass="filter-button" text="修改到手价" />
                                                </children>
                                                <padding>
                                                    <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
                                                </padding>
                                            </HBox>
                                        </VBox>
                                    </top>
                                    <center>
                                        <ScrollPane fx:id="onSaleScrollPane" fitToWidth="true" prefHeight="200.0" prefWidth="200.0" styleClass="scroll-pane" BorderPane.alignment="CENTER">
                                            <content>
                                                <FlowPane fx:id="onSaleItemsContainer" hgap="15.0" styleClass="cards-container" vgap="15.0">
                                                    <padding>
                                                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                                    </padding>
                                                </FlowPane>
                                            </content>
                                        </ScrollPane>
                                    </center>
                                </BorderPane>
                            </children>
                        </StackPane>

                        <!-- 确认面板 -->
                        <StackPane fx:id="detailPane" prefHeight="150.0" prefWidth="200.0" visible="false">
                            <children>
                                <!-- 动态加载IO661流水列表界面 -->
                            </children>
                        </StackPane>

                        <!-- 报价面板 -->
                        <StackPane fx:id="pricePane" prefHeight="150.0" prefWidth="200.0" visible="false">
                            <children>
                                <Label text="报价内容将在这里显示" />
                            </children>
                        </StackPane>

                        <!-- 订单面板 -->
                        <StackPane fx:id="ordersPane" prefHeight="150.0" prefWidth="200.0" visible="false">
                            <children>
                                <!-- 动态加载IO661订单列表界面 -->
                            </children>
                        </StackPane>

                        <!-- 加载指示器 -->
                        <StackPane fx:id="loadingPane" prefHeight="150.0" prefWidth="200.0" style="-fx-background-color: rgba(0, 0, 0, 0.5);" visible="false">
                            <children>
                                <VBox alignment="CENTER" spacing="10">
                                    <ProgressIndicator prefHeight="50.0" prefWidth="50.0" />
                                    <Label text="加载中..." textFill="WHITE" />
                                </VBox>
                            </children>
                        </StackPane>
                    </children>
                </StackPane>
            </center>
        </BorderPane>
    </center>
</BorderPane>

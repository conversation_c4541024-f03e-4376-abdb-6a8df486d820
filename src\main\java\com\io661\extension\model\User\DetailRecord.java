package com.io661.extension.model.User;

import lombok.Data;

/**
 * 提现记录数据模型
 */
@Data
public class DetailRecord {
    /**
     * 交易金额（分）
     */
    private Integer amount;
    /**
     * 交易后余额
     */
    private Integer balance;
    /**
     * 交易类型
     */
    private String type;
    /**
     * 关联订单号
     */
    private String orderNo;
    /**
     * 交易时间
     */
    private String createTime;
}

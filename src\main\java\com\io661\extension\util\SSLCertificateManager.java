package com.io661.extension.util;

import javax.net.ssl.*;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

/**
 * SSL证书管理器
 * 用于配置自定义SSL证书以避免HTTPS握手失败
 */
public class SSLCertificateManager {

    private static SSLContext sslContext;
    private static boolean initialized = false;

    // 静态初始化块，确保在类加载时就初始化SSL上下文
    static {
        // 强制设置SSL系统属性（针对打包后的环境）
        forceSSLSystemProperties();
        initializeSSLContext();
    }

    /**
     * 强制设置SSL系统属性（针对打包后的环境）
     */
    private static void forceSSLSystemProperties() {
        try {
            // 禁用SSL验证相关的系统属性
            System.setProperty("javax.net.ssl.trustStore", "");
            System.setProperty("javax.net.ssl.trustStorePassword", "");
            System.setProperty("javax.net.ssl.keyStore", "");
            System.setProperty("javax.net.ssl.keyStorePassword", "");

            // 禁用各种SSL检查
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");
            System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
            System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true");
            System.setProperty("jdk.tls.allowUnsafeServerCertChange", "true");
            System.setProperty("jdk.tls.allowUnsafeRenegotiation", "true");

            // 设置TLS版本
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3");

            // 禁用OCSP检查
            System.setProperty("com.sun.security.enableCRLDP", "false");
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        } catch (Exception e) {
            System.err.println("设置SSL系统属性失败: " + e.getMessage());
        }
    }

    /**
     * 初始化SSL上下文，加载自定义证书
     */
    public static synchronized void initializeSSLContext() {
        if (initialized) {
            return;
        }

        try {
            // 首先尝试加载自定义证书，如果失败则使用信任所有证书的模式
            if (!loadCustomCertificates()) {
                System.out.println("自定义证书加载失败，使用信任所有证书模式");
                createTrustAllSSLContext();
            }
        } catch (Exception e) {
            System.err.println("SSL证书配置失败: " + e.getMessage());
            // 作为最后的备选方案，使用信任所有证书的模式
            try {
                createTrustAllSSLContext();
                System.out.println("使用备选SSL配置成功");
            } catch (Exception fallbackException) {
                System.err.println("备选SSL配置也失败: " + fallbackException.getMessage());
            }
        }
    }

    /**
     * 加载自定义证书
     * @return true 如果证书加载成功，false 如果失败
     */
    private static boolean loadCustomCertificates() {
        try {
            // 创建证书工厂
            CertificateFactory cf = CertificateFactory.getInstance("X.509");

            // 加载证书文件 - 优先从classpath加载
            InputStream certInputStream = SSLCertificateManager.class.getResourceAsStream("/cert/fullchain.pem");
            if (certInputStream == null) {
                // 如果资源文件不存在，尝试从文件系统加载（开发环境）
                try {
                    certInputStream = new FileInputStream("src/main/resources/cert/fullchain.pem");
                } catch (Exception e) {
                    System.err.println("无法找到证书文件: " + e.getMessage());
                    return false;
                }
            }

            // 读取所有证书（证书链）
            java.util.Collection<? extends Certificate> certificates = cf.generateCertificates(certInputStream);
            certInputStream.close();

            if (certificates.isEmpty()) {
                System.err.println("证书文件中没有找到有效证书");
                return false;
            }

            // 获取默认的TrustStore
            KeyStore defaultKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            defaultKeyStore.load(null, null);

            // 获取系统默认的TrustManager
            TrustManagerFactory defaultTmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            defaultTmf.init((KeyStore) null); // 使用系统默认的TrustStore

            // 创建新的KeyStore，包含系统证书和自定义证书
            KeyStore combinedKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            combinedKeyStore.load(null, null);

            // 添加自定义证书
            int i = 0;
            for (Certificate cert : certificates) {
                combinedKeyStore.setCertificateEntry("custom_cert" + i, cert);
                i++;
                System.out.println("已添加自定义证书: " + ((X509Certificate) cert).getSubjectX500Principal());
            }

            // 创建组合的TrustManager
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(combinedKeyStore);

            // 创建SSL上下文
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, tmf.getTrustManagers(), null);

            initialized = true;
            System.out.println("SSL证书配置成功，共加载 " + certificates.size() + " 个自定义证书");
            return true;

        } catch (Exception e) {
            System.err.println("加载自定义证书失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建信任所有证书的SSL上下文（用于兼容性，特别是打包后的环境）
     */
    private static void createTrustAllSSLContext() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0]; // 返回空数组而不是null
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有客户端证书
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有服务器证书
                    }
                }
        };

        // 尝试不同的SSL协议
        String[] protocols = {"TLS", "TLSv1.2", "TLSv1.3", "SSL"};
        Exception lastException = null;

        for (String protocol : protocols) {
            try {
                sslContext = SSLContext.getInstance(protocol);
                sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

                // 设置为默认SSL上下文（重要：针对打包后的环境）
                SSLContext.setDefault(sslContext);

                initialized = true;
                System.out.println("已启用信任所有证书的SSL上下文，协议: " + protocol);
                return;
            } catch (Exception e) {
                lastException = e;
                System.err.println("SSL协议 " + protocol + " 初始化失败: " + e.getMessage());
            }
        }
        // 如果所有协议都失败，抛出最后一个异常
        throw lastException;
    }

    /**
     * 获取配置好的SSL上下文
     */
    public static SSLContext getSSLContext() {
        if (!initialized) {
            initializeSSLContext();
        }
        return sslContext;
    }

    /**
     * 获取配置好的SSLSocketFactory
     */
    public static SSLSocketFactory getSSLSocketFactory() {
        return getSSLContext().getSocketFactory();
    }

    /**
     * 获取配置好的HostnameVerifier
     */
    public static HostnameVerifier getHostnameVerifier() {
        // 对于io661.com域名，我们信任证书
        return (hostname, session) -> {
            if ("io661.com".equals(hostname)) {
                return true;
            }
            // 对于其他域名，使用默认验证
            return HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
        };
    }
}

package com.io661.extension.task;


import com.io661.extension.model.Steam.IO661TradeOrdersReq;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.util.User.UserCookieManager;
import javafx.animation.AnimationTimer;

public class ConfirmOrderTask {
    static TransactionAssistantServiceImpl transactionAssistantService = new TransactionAssistantServiceImpl();

    String token = UserCookieManager.readToken();
    /**
     * 确认报价消息提示
     */
    public static void confirmOrderMessage() {
        AnimationTimer timer = new AnimationTimer() {
            private long lastUpdate = 0;
            private int count = 0;
            final IO661TradeOrdersReq req = new IO661TradeOrdersReq();

            // 获取待卖家发货
            // type=2&subType=2
            @Override
            public void handle(long now) {
                if (now - lastUpdate > 1000) {
                    lastUpdate = now;
                    count++;
                    transactionAssistantService.getAllOrders(req);

                }
            }
        };

    }
}

.contact-card {
    /*-fx-border-width: 1px;*/
    /*-fx-border-style: solid;*/
    /*-fx-border-color: #ff0000;*/
    -fx-pref-width: 100%;
    -fx-pref-height: 100%;
    -fx-border-width: 0px;
    -fx-background-color: #fff;
}

.title-label {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.box {
    /*-fx-border-width: 1px;*/
    /*-fx-border-style: solid;*/
    /*-fx-border-color: #f0f;*/
    -fx-pref-width: 100%;
    -fx-pref-height: 100%;
    -fx-hgrow: ALWAYS;
    -fx-vgrow: ALWAYS;
    -fx-background-color: #fff;
}

.top {
    /*-fx-border-width: 1px;*/
    /*-fx-border-style: solid;*/
    /*-fx-border-color: #abf;*/
}

.title-label{
    -fx-padding: 0 0 20px 0;
}

.fill-region {
    -fx-vgrow: ALWAYS;
}

.bottom {
    -fx-min-height: 80px;
    /*-fx-border-width: 1px;*/
    /*-fx-border-style: solid;*/
    /*-fx-border-color: #abf;*/
}

.advise {
    -fx-font-size: 12px;
    -fx-padding: 15px 0 0 0;
}

.version-bottom {
    -fx-pref-width: 100%;
    /*-fx-border-width: 1px;*/
    /*-fx-border-style: solid;*/
    /*-fx-border-color: #373838;*/
    -fx-font-size: 14px;
    -fx-padding: 5px 0 10px 0;
}

.split-line {
    -fx-padding: 0 20px;
}

.hint-label {
    -fx-font-size: 16px;
}

.help-label {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 20px 0;
}

.contact-button {
    -fx-font-size: 14px;
    -fx-text-fill: #fff;
    -fx-font-weight: bold;
    -fx-background-color: #43c3dc;
    -fx-cursor: hand;
}

.contact-button:hover {
    -fx-background-color: #37D4CF;
}

.contact-button:pressed {
    -fx-background-color: #0DA5AA;
}
package com.io661.extension.model.Steam;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SteamInventoryResponse {
    /**
     * 总页数
     */
    private int pages;

    /**
     * 总记录数
     */
    private int total;

    /**
     * 总数量
     */
    private int count;

    /**
     * 物品列表
     */
    private List<InventoryDataList> list;

    @Data
    public static class InventoryDataList {
        /**
         * 库存表对应ID
         */
        private Long id;
        /**
         * 库存表对应ID列表
         */
        private String ids;
        /**
         * 模板HashName
         */
        private String hashName;
        /**
         * 展示物品昵称
         */
        private String itemName;
        /**
         * 物品图片
         */
        private String iconUrl;
        /**
         * Steam的assetId
         */
        private String assetId;
        /**
         * 饰品数量
         */
        private Integer quantity;
        /**
         * 当前在售状态
         */
        private boolean onSell;
        /**
         * 当前售出价格
         */
        private Integer onSellPrice;
        /**
         * 所属steamId
         */
        private String steamId;
        /**
         * 参考价格（分）
         */
        private Integer refPrice;
        /**
         * 磨损值
         */
        private BigDecimal floatValue;
        /**
         * 印花信息
         */
        private List<CommonSticker> stickerList;
        /**
         * 可否交易
         */
        private Boolean tradable;
        /**
         * 平台类型标识（io661, youpin等）
         */
        private String platformType;

        /**
         * 多平台在售状态（存储所有在售平台的列表）
         */
        private List<String> onSellPlatforms;
        /**
         * 可交易时间
         */
        private LocalDateTime tradableTime;
    }
}

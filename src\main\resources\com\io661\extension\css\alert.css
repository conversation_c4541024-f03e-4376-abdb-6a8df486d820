/* alert.css - 自定义Alert样式 */
.dialog-pane {
    -fx-background-color: #F9FAFB;   /* 背景色 */
    -fx-border-color: #E5E7EB;      /* 边框色 */
    -fx-border-width: 1px;          /* 边框宽度 */
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4); /* 阴影 */
}

/* 内容文本样式 */
.dialog-pane .label {
    -fx-font-family: "Microsoft YaHei", sans-serif; /* 字体 */
    -fx-font-size: 14px;                             /* 字号 */
    -fx-text-fill: #374151;                          /* 文字颜色 */
    -fx-padding: 16px 24px;                          /* 内边距 */
}

/* 按钮栏样式（居中对齐） */
.dialog-pane .button-bar {
    -fx-alignment: center;      /* 按钮居中 */
    -fx-padding: 12px 24px;     /* 内边距 */
}

/* 按钮基础样式 */
.dialog-pane .button {
    -fx-background-color: #60A5FA; /* 按钮背景色 */
    -fx-text-fill: white;          /* 文字颜色 */
    -fx-font-size: 14px;           /* 字号 */
    -fx-padding: 8px 24px;         /* 内边距 */
    -fx-border-radius: 4px;        /* 圆角 */
    -fx-background-radius: 4px;    /* 圆角 */
    -fx-cursor: hand;              /* 鼠标手势 */
}

/* 按钮 hover 效果 */
.dialog-pane .button:hover {
    -fx-background-color: #3B82F6; /* 深色 hover */
}

/* 图标位置调整（与文本间距） */
.dialog-pane .graphic {
    -fx-padding: 0 12px 0 24px; /* 图标左侧留空，与文本间距12px */
}
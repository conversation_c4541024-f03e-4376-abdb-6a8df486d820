<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="775.0" prefWidth="1368.0" xmlns="http://javafx.com/javafx/21" fx:controller="com.io661.extension.controller.MainWindowController" stylesheets="@../css/main-window.css">
    <left>
        <VBox prefHeight="750" prefWidth="100" spacing="15" style="-fx-background-color: #f4f4f4;">
            <padding>
                <Insets bottom="10" left="10" right="10" top="10" />
            </padding>
            <!--            <Label prefHeight="126" fx:id="mainWindowLogo" />-->
            <ImageView fitHeight="64.0" fitWidth="64.0" pickOnBounds="true" preserveRatio="true">
                <Image url="@../img/Logo.png"/>
                <VBox.margin>
                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                </VBox.margin>
            </ImageView>

            <!-- 账号管理 -->
            <VBox fx:id="accountManagementMenu" alignment="CENTER" styleClass="menu-item" onMouseClicked="#showAccountManagement">
                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                    <Image url="@../img/accountManage.png" />
                </ImageView>
                <Label text="账号管理" styleClass="menu-text" />
            </VBox>

            <!-- 交易助手 -->
            <VBox fx:id="transactionAssistantMenu" alignment="CENTER" styleClass="menu-item" onMouseClicked="#showTransactionAssistant">
                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                    <Image url="@../img/tradeAssistant.png" />
                </ImageView>
                <Label text="交易助手" styleClass="menu-text" />
            </VBox>

            <!-- 个人设置 -->
            <VBox fx:id="personalSettingMenu" alignment="CENTER" styleClass="menu-item" onMouseClicked="#personalSetting">
                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                    <Image url="@../img/personalSetting.png" />
                </ImageView>
                <Label text="个人设置" styleClass="menu-text" />
            </VBox>

            <!-- 联系我们 -->
            <VBox fx:id="contactUsMenu" alignment="CENTER" styleClass="menu-item" onMouseClicked="#contactUs">
                <ImageView fitHeight="40" fitWidth="40" preserveRatio="true">
                    <Image url="@../img/contactUs.png" />
                </ImageView>
                <Label text="联系我们" styleClass="menu-text" />
            </VBox>

            <Region VBox.vgrow="ALWAYS" />

            <!-- 用户个人信息图标 -->
            <VBox fx:id="userInfo" alignment="CENTER" styleClass="user-info-item" onMouseClicked="#showUserInfo">
                <ImageView fx:id="userAvatarIcon" fitHeight="40" fitWidth="40" preserveRatio="true" style="-fx-background-radius: 20; -fx-border-radius: 20;">
                    <Image url="@../img/Logo.png" />
                </ImageView>
            </VBox>
        </VBox>
    </left>
    <center>
        <StackPane fx:id="contentArea" prefHeight="768.0" prefWidth="Infinity" maxWidth="Infinity" maxHeight="Infinity" VBox.vgrow="ALWAYS" HBox.hgrow="ALWAYS">
            <TableView fx:id="accountTable" visible="false">
                <columns>
                    <TableColumn fx:id="accountNameColumn" text="账号名称" />
                    <TableColumn fx:id="groupColumn" text="分组" />
                    <TableColumn fx:id="ipAddressColumn" text="IP地址" />
                    <TableColumn fx:id="statusColumn" text="状态" />
                    <TableColumn fx:id="actionColumn" text="操作" />
                </columns>
            </TableView>
        </StackPane>
    </center>

    <bottom>
        <HBox alignment="CENTER_LEFT" prefWidth="1368.0" spacing="10" style="-fx-background-color: #f0f0f0;">
            <padding>
                <Insets bottom="5" left="10" right="10" top="5" />
            </padding>
            <Label fx:id="versionCode" text="版本号" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="statusLabel" text="状态" />
        </HBox>
    </bottom>
</BorderPane>

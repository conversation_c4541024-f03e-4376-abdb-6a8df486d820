<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="500.0" prefWidth="600.0" styleClass="market-listing-dialog" stylesheets="@../css/market-listing-dialog.css" xmlns="http://javafx.com/javafx/21" fx:controller="com.io661.extension.controller.MarketListingDialogController">
    <top>
        <HBox alignment="CENTER_LEFT" spacing="10" styleClass="dialog-header">
            <Label styleClass="dialog-title" text="Steam出售" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="closeButton" onAction="#handleClose" styleClass="close-button" text="×" />
            <padding>
                <Insets bottom="10" left="15" right="15" top="10" />
            </padding>
        </HBox>
    </top>

    <center>
        <VBox spacing="15" styleClass="dialog-content">
            <padding>
                <Insets bottom="15" left="15" right="15" top="15" />
            </padding>

            <!-- 饰品信息 -->
            <Label styleClass="warning-text" text="饰品由于价格存在差异，请仔细检验价格后确认上架" />

            <!-- 选择上架平台 -->
            <VBox spacing="10" styleClass="platform-selection-container">
                <Label styleClass="section-title" text="选择上架平台" />
                <HBox alignment="CENTER_LEFT" spacing="15">
                    <CheckBox fx:id="io661Checkbox" disable="true" selected="true" text="IO661" />
                    <!--                    <CheckBox fx:id="uuCheckbox" text="UU"/>-->
                    <!--                    <CheckBox fx:id="buffCheckbox" text="BUFF"/>-->
                </HBox>
            </VBox>

            <!-- 饰品列表 -->
            <VBox spacing="10" styleClass="items-container" VBox.vgrow="ALWAYS">
                <Label styleClass="section-title" text="饰品列表" />
                <ScrollPane fitToWidth="true" styleClass="items-scroll-pane" VBox.vgrow="ALWAYS">
                    <VBox fx:id="itemsContainer" spacing="5" styleClass="items-list">
                        <!-- 物品将在控制器中动态添加 -->
                    </VBox>
                </ScrollPane>
            </VBox>
        </VBox>
    </center>
    <bottom>

        <HBox alignment="CENTER_RIGHT" prefHeight="56.0" prefWidth="621.0" spacing="10" styleClass="dialog-footer">
            <ComboBox fx:id="referencePriceComboBox" prefWidth="120" promptText="选择参考价格比例"  />
            <Button fx:id="oneClickPricingButton" prefWidth="80" styleClass="oneClickPricingButton" text="一键定价" onAction="#handleOneClickOnSale" />
            <Region prefHeight="25.0" prefWidth="199.0" />
            <Button fx:id="cancelButton" onAction="#handleCancel" styleClass="cancel-button" text="取消" />
            <Button fx:id="confirmButton" onAction="#handleConfirm" styleClass="confirm-button" text="确定上架" />
            <padding>
                <Insets bottom="15" left="15" right="15" top="15" />
            </padding>
        </HBox>
    </bottom>
</BorderPane>

.market-listing-dialog {
    -fx-background-color: white;
    -fx-border-color: #ddd;
    -fx-border-width: 1px;
}

.dialog-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #ddd;
    -fx-border-width: 0 0 1px 0;
}

.dialog-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

.close-button {
    -fx-background-color: transparent;
    -fx-font-size: 16px;
    -fx-cursor: hand;
}

.close-button:hover {
    -fx-text-fill: #ff4d4f;
}

.dialog-content {
    -fx-background-color: white;
}

.warning-text {
    -fx-text-fill: #ff4d4f;
    -fx-font-size: 12px;
}

.section-title {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.platform-selection-container {
    -fx-padding: 10px;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
    -fx-border-color: #ddd;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
}

.price-input-container {
    -fx-padding: 10px;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
    -fx-border-color: #ddd;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
}

.price-input {
    -fx-pref-width: 200px;
}

.help-icon {
    -fx-text-fill: #1890ff;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.error-text {
    -fx-text-fill: #ff4d4f;
    -fx-font-size: 12px;
}

.items-container {
    -fx-padding: 10px;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
    -fx-border-color: #ddd;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
}

.items-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: #ddd;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
}

.items-scroll-pane > .viewport {
    -fx-background-color: white;
}

.items-list {
    -fx-background-color: white;
    -fx-padding: 5px;
}

.item-row {
    -fx-padding: 5px;
    -fx-background-color: white;
    -fx-border-color: #ddd;
    -fx-border-width: 0 0 1px 0;
}

.item-row:hover {
    -fx-background-color: #f0f0f0;
}

.item-image {
    -fx-fit-width: 40px;
    -fx-fit-height: 40px;
    -fx-preserve-ratio: true;
}

.item-name {
    -fx-font-weight: bold;
}

.item-price {
    -fx-text-fill: #ff4d4f;
    -fx-font-weight: bold;
}

.dialog-footer {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #ddd;
    -fx-border-width: 1px 0 0 0;
}

.cancel-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333;
    -fx-padding: 5px 15px;
    -fx-cursor: hand;
}

.confirm-button {
    -fx-background-color: #1890ff;
    -fx-text-fill: white;
    -fx-padding: 5px 15px;
    -fx-cursor: hand;
}

.confirm-button:hover {
    -fx-background-color: #40a9ff;
}

.cancel-button:hover {
    -fx-background-color: #e0e0e0;
}

.oneClickPricingButton {
    -fx-background-color: #1890ff;
    -fx-text-fill: white;
    -fx-padding: 5px 15px;
    -fx-cursor: hand;
}

.oneClickPricingButton:hover {
    -fx-background-color: #40a9ff;
}

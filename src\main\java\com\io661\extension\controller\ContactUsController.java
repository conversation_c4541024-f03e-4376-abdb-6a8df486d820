package com.io661.extension.controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import lombok.Data;

import java.awt.*;
import java.net.URI;
import java.net.URL;
import java.util.ResourceBundle;

@Data
public class ContactUsController implements Initializable {

    @FXML
    private Region fillRegion;

    @FXML
    private VBox contactUsBox;

    @FXML
    private Button officialWebsite;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        VBox.setVgrow(fillRegion, Priority.ALWAYS);
    }

    public void GoWebsite() {
        try {
            // 使用Desktop类打开浏览器并跳转到指定URL
            URI uri = new URI("https://io661.com");
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().browse(uri);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

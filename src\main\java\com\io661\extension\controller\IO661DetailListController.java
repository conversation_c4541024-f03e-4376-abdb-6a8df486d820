package com.io661.extension.controller;


import com.io661.extension.model.User.DetailHistoryReq;
import com.io661.extension.model.User.DetailHistoryRes;
import com.io661.extension.model.User.DetailRecord;
import com.io661.extension.service.Impl.WithdrawServiceImpl;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;

import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

import static com.io661.extension.util.User.UserCookieManager.readToken;

public class IO661DetailListController implements Initializable {
    private final String token = readToken();

    private final int pageSize = 15;
    // 数据相关
    private final List<DetailRecord> allDetails = new ArrayList<>();
    @FXML
    public ComboBox<String> detailTypeComboBox;
    @FXML
    public ScrollPane detailScrollPane;
    @FXML
    public VBox detailListContainer;
    // FXML 控件
    @FXML
    private TextField searchField;
    @FXML
    private Button searchButton;
    @FXML
    private Button refreshButton;
    @FXML
    private Label detailSummaryLabel;
    @FXML
    private StackPane loadingPane;
    @FXML
    private VBox noDataPane;
    // 服务类
    private WithdrawServiceImpl withdrawService;
    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    // 查询参数
    private Long currentType = 0L; // 默认全部
    private String currentSearch = "";

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 初始化服务
        withdrawService = new WithdrawServiceImpl();

        // 初始化界面
        initializeUI();

        // 设置事件处理
        setupEventHandlers();

        // 初始加载订单数据
        refreshDetails();
    }

    /**
     * 初始化界面
     */
    private void initializeUI() {
        // 初始化流水类型下拉框

        // type 全部 0 充值提现 1 购买饰品 2 出售饰品 3 平台操作 4 其他 5
        ObservableList<String> detailType = FXCollections.observableArrayList();
        detailType.addAll("全部", "充值提现", "购买饰品", "出售饰品", "平台操作", "其他");
        detailTypeComboBox.setItems(detailType);
        detailTypeComboBox.getSelectionModel().select(0); // 默认选择"全部"
        
        // 设置滚动监听器实现上拉加载
        detailScrollPane.vvalueProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue.doubleValue() >= 0.95 && !isLoading && hasMoreData) {
                loadMoreOrders();
            }
        });
    }

    /**
     * 设置事件处理
     */
    private void setupEventHandlers() {
        // 流水类型选择事件
        // type 全部 0 充值提现 1 购买饰品 2 出售饰品 3 平台操作 4 其他 5
        detailTypeComboBox.setOnAction(event -> {
            int selectedIndex = detailTypeComboBox.getSelectionModel().getSelectedIndex();
            switch (selectedIndex) {
                case 1: // 充值提现
                    currentType = 1L;
                    break;
                case 2: // 购买饰品
                    currentType = 2L;
                    break;
                case 3: // 出售饰品
                    currentType = 3L;
                    break;
                case 4: // 平台操作
                    currentType = 4L;
                    break;
                case 5: // 其他
                    currentType = 5L;
                    break;
                default: // 全部
                    currentType = 0L; // 默认显示全部
                    break;
            }
            refreshDetails();
        });

        // 搜索按钮事件
        searchButton.setOnAction(event -> performSearch());

        // 搜索框回车事件
        searchField.setOnAction(event -> performSearch());

        // 刷新按钮事件
        refreshButton.setOnAction(event -> refreshDetails());
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        currentSearch = searchField.getText().trim();
        refreshDetails();
    }

    private void refreshDetails() {
        currentPage = 1;
        hasMoreData = true;
        allDetails.clear();
        detailListContainer.getChildren().clear();
        loadDetails();
    }

    /**
     * 加载更多订单(动态加载)
     */
    private void loadMoreOrders() {
        if (!isLoading && hasMoreData) {
            currentPage++;
            loadDetails();
        }
    }

    /**
     * 动态加载流水逻辑
     */
    private void loadDetails() {
        if (isLoading) return;

        isLoading = true;
        showLoading(true);

        CompletableFuture.supplyAsync(() -> {
            try {
                DetailHistoryReq request = new DetailHistoryReq();
                request.setPage((long) currentPage);
                request.setLimit((long) pageSize);
                request.setType(currentType);
                request.setSearch(currentSearch);

                return withdrawService.detailHistory(request, token);
            } catch (Exception e) {
                System.err.println("加载订单失败: " + e.getMessage());
                return new DetailHistoryRes();
            }
        }).thenAccept(response -> {
            Platform.runLater(() -> {
                isLoading = false;
                showLoading(false);

                if (response != null && response.getCode() == 0 && response.getData() != null) {
                    List<DetailRecord> newOrders = response.getData().getList();

                    if (newOrders != null && !newOrders.isEmpty()) {
                        allDetails.addAll(newOrders);
                        addOrdersToUI(newOrders);

                        // 检查是否还有更多数据
                        hasMoreData = newOrders.size() >= pageSize;
                    } else {
                        hasMoreData = false;
                    }

                    updateSummaryLabel();
                    updateNoDataVisibility();
                } else {
                    hasMoreData = false;
                    updateNoDataVisibility();
                }
            });
        });
    }

    private void addOrdersToUI(List<DetailRecord> orders) {
        for (DetailRecord order : orders) {
            VBox orderCard = createOrderCard(order);
            detailListContainer.getChildren().add(orderCard);
        }
    }

    /**
     * 订单详情展开
     */
    private VBox createOrderCard(DetailRecord detail) {
        VBox card = new VBox();
        card.getStyleClass().add("detail-card");
        card.setPadding(new Insets(15));
        // 让 VBox 内容水平居中
        card.setAlignment(Pos.CENTER);


        // 主要内容区域 - 单行布局，设置水平居中
        HBox mainContent = new HBox(15);
        mainContent.setAlignment(Pos.CENTER);

        // 1. 订单信息列 - 订单号
        VBox orderInfoCol = new VBox(3);
        orderInfoCol.setPrefWidth(230);
        orderInfoCol.setAlignment(Pos.CENTER_LEFT);

        Label orderNoLabel = new Label(detail.getOrderNo() != null? detail.getOrderNo() : "---------");
        orderNoLabel.getStyleClass().add("detail-no-label");

        orderInfoCol.getChildren().addAll(orderNoLabel);

        // 2.余额增减
        VBox itemInfo = new VBox(3);
        itemInfo.setPrefWidth(180); // 与表头宽度匹配
        itemInfo.setAlignment(Pos.CENTER); // 让该列内容居中

        // 获取金额并格式化
        String amountText = detail.getAmount() != null
                ? String.format("%.2f", detail.getAmount() / 100.0)
                : "0.00";

        Label amountLabel = new Label(amountText);
        amountLabel.getStyleClass().add("amount-label");
        amountLabel.setWrapText(true);
        amountLabel.setMaxWidth(180);
        amountLabel.setAlignment(Pos.CENTER);

        // 根据金额正负设置文本颜色
        double amountValue = detail.getAmount() != null
                ? detail.getAmount() / 100.0
                : 0.0;

        if (amountValue < 0) {
            amountLabel.setStyle("-fx-text-fill: red;");
        } else if (amountValue > 0) {
            amountLabel.setStyle("-fx-text-fill: green;");
        }

        itemInfo.getChildren().add(amountLabel);

        // 3. 流水类型
        VBox typeCol = new VBox();
        typeCol.setPrefWidth(220); // 与表头宽度匹配
        typeCol.setAlignment(Pos.CENTER); // 让该列内容居中

        Label typeLabel = new Label(detail.getType() != null? detail.getType() : "系统核验中");
        typeLabel.getStyleClass().add("type-label");
        typeCol.getChildren().addAll(typeLabel);


        // 4. 余额（元） (15%)
        VBox balanceCol = new VBox(3);
        balanceCol.setPrefWidth(150); // 与表头宽度匹配
        balanceCol.setAlignment(Pos.CENTER); // 让该列内容居中

        DecimalFormat df = new DecimalFormat("#0.00");
        // 将分转换为元
        double priceInYuan = detail.getBalance() != null? detail.getBalance() / 100.0 : 0.0;

        Label balanceBabel = new Label("¥" + df.format(priceInYuan));
        balanceBabel.getStyleClass().add("balance-label");

        balanceCol.getChildren().addAll(balanceBabel);

        // 5. 创建时间 (22%)
        VBox createTimeCol = new VBox();
        createTimeCol.setPrefWidth(220); // 与表头宽度匹配
        createTimeCol.setAlignment(Pos.CENTER_RIGHT); // 让该列内容居中

        Label createTimeLabel = new Label(detail.getCreateTime() != null? detail.getCreateTime(): "1970-01-01 00:00:00");
        createTimeLabel.getStyleClass().addAll("createTime-label");
        createTimeCol.getChildren().add(createTimeLabel);

        // 组装主要内容，按表头对应列添加
        mainContent.getChildren().addAll(orderInfoCol, itemInfo, typeCol, balanceCol, createTimeCol);

        card.getChildren().add(mainContent);
        return card;
    }


    private void showLoading(boolean show) {
        loadingPane.setVisible(show);
    }

    private void updateSummaryLabel() {
        Platform.runLater(() -> detailSummaryLabel.setText("共 " + allDetails.size() + " 条流水"));
    }

    private void updateNoDataVisibility() {
        Platform.runLater(() -> {
            boolean hasData = !allDetails.isEmpty();
            noDataPane.setVisible(!hasData);
            detailScrollPane.setVisible(hasData);
        });
    }
}

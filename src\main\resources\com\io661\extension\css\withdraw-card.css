/* 提现卡片样式 */

.withdraw-card {
    -fx-background-color: linear-gradient(to bottom, #2c2c2c, #1e1e1e);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 15, 0, 0, 3);
    -fx-border-color: #404040;
    -fx-border-width: 1px;
}


/* 标题样式 */

.withdraw-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #ffffff;
    -fx-padding: 0 0 10 0;
    -fx-text-alignment: CENTER; /* 添加居中属性 */
}


/* 关闭按钮样式 */

.close-buttonx {
    -fx-background-color: transparent;
    -fx-text-fill: #ffffff;
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-background-radius: 50%;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
}

.close-buttonx:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-text-fill: #ff6b6b;
}

.close-buttonx:pressed {
    -fx-background-color: rgba(255, 255, 255, 0.2);
}


/* 可提现金额样式 */

.available-amount-label {
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: normal;
}

.available-amount-value {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
}


/* 输入框样式 */

.withdraw-input {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-prompt-text-fill: #888888;
}

.withdraw-input:focused {
    -fx-border-color: #00CED1;
    -fx-border-width: 2px;
    -fx-background-color: #404040;
}


/* 标签样式 */

.info-label {
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
}

.value-label {
    -fx-text-fill: #4CAF50;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.fee-label {
    -fx-text-fill: #FFA726;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}


/* 账号显示样式 */

.account-display {
    -fx-background-color: #3c3c3c;
    -fx-text-fill: #ffffff;
    -fx-font-size: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-opacity: 0.8;
}


/* 提现须知样式 */

.notice-title {
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-padding: 10 0 5 0;
}

.notice-item {
    -fx-text-fill: #cccccc;
    -fx-font-size: 12px;
    -fx-padding: 2 0;
}


/* 确定提现按钮样式 */

.withdraw-button {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
    -fx-text-fill: #ffffff;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-padding: 12px 30px;
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.3), 8, 0, 0, 2);
}

.withdraw-button:hover {
    -fx-background-color: linear-gradient(to bottom, #00E5E8, #00CED1);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.5), 12, 0, 0, 3);
}

.withdraw-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #00B8CC, #00A5B8);
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.2), 4, 0, 0, 1);
}


/* 提现操作面板样式 */

.info-row {
    -fx-spacing: 10px;
    -fx-alignment: center-left;
}

.available-amount-label {
    -fx-text-fill: #cccccc;
    -fx-font-size: 14px;
}

.available-amount-value {
    -fx-text-fill: #00CED1;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
}

.info-label {
    -fx-text-fill: #cccccc;
    -fx-font-size: 13px;
}

.value-label {
    -fx-text-fill: #00CED1;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.fee-label {
    -fx-text-fill: #ff9999;
    -fx-font-size: 14px;
}

.withdraw-input,
.account-display {
    -fx-background-color: #2c2c2c;
    -fx-text-fill: white;
    -fx-border-color: #404040;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8px;
}

.withdraw-input:focused,
.account-display:focused {
    -fx-border-color: #00CED1;
}

.notice-container {
    -fx-background-color: rgba(0, 206, 209, 0.1);
    -fx-border-color: #00CED1;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 10px;
}

.notice-title {
    -fx-text-fill: #00CED1;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.notice-item {
    -fx-text-fill: #cccccc;
    -fx-font-size: 12px;
}

.withdraw-button:disabled {
    -fx-background-color: #666666;
    -fx-text-fill: #999999;
    -fx-effect: none;
    -fx-cursor: default;
}


/* 容器样式 */

.withdraw-container {
    -fx-spacing: 15px;
    -fx-padding: 20px;
}

.info-row {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.amount-row {
    -fx-spacing: 20px;
    -fx-alignment: CENTER_LEFT;
}


/* 动画效果 */

.withdraw-card {
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
}


/* 面板切换动画 */

.panel {
    -fx-opacity: 1;
    -fx-scale-x: 1;
    -fx-scale-y: 1;
}

.panel.active {
    -fx-opacity: 1;
    -fx-scale-x: 1;
    -fx-scale-y: 1;
}


/* 选项卡按钮样式 */

.tab-button {
    -fx-background-color: transparent;
    -fx-text-fill: #aaaaaa;
    -fx-font-size: 14px;
    -fx-padding: 8px 20px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
    -fx-effect: null;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
    -fx-text-alignment: CENTER; /* 添加居中属性 */
}

.tab-button.active {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.3), 6, 0, 0, 2);
}

.tab-button:hover {
    -fx-background-color: rgba(0, 184, 204, 0.15);
    -fx-text-fill: #00CED1;
}

.tab-button:pressed {
    -fx-background-color: rgba(0, 184, 204, 0.3);
}


/* 基础卡片样式 */

.withdraw-card {
    -fx-background-color: #1e1e1e;
    -fx-padding: 10px;
}

.withdraw-container {
    -fx-background-color: transparent;
    -fx-spacing: 15px;
    -fx-padding: 20px;
}

.page-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}


/* 选项卡按钮样式 */

.tab-button {
    -fx-background-color: transparent;
    -fx-text-fill: #aaaaaa;
    -fx-font-size: 14px;
    -fx-padding: 8px 20px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
    -fx-effect: null;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
}

.tab-button.active {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(0, 206, 209, 0.3), 6, 0, 0, 2);
}

.tab-button:hover {
    -fx-background-color: rgba(0, 184, 204, 0.15);
    -fx-text-fill: #00CED1;
}

.tab-button:pressed {
    -fx-background-color: rgba(0, 184, 204, 0.3);
}


/* 内容容器样式 */

.content-container {
    -fx-background-color: transparent;
    -fx-padding: 0px;
}

.panel {
    -fx-background-color: transparent;
    -fx-opacity: 1;
}


/* 筛选区域样式 */

.filter-label {
    -fx-text-fill: white;
    -fx-font-size: 13px;
}

.date-picker {
    -fx-background-color: #2c2c2c;
    -fx-text-fill: white;
    -fx-border-color: #404040;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.status-combo {
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.search-button,
.reset-button {
    -fx-background-color: #404040;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 12px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.search-button:hover,
.reset-button:hover {
    -fx-background-color: #505050;
}

.search-button {
    -fx-background-color: linear-gradient(to bottom, #00CED1, #00B8CC);
}

.search-button:hover {
    -fx-background-color: linear-gradient(to bottom, #00E5E8, #00CED1);
}


/* 表格样式优化 */
.record-table {
    -fx-background-color: #2d2d2d; /* 黑灰色背景 */
    -fx-border-color: #404040;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.record-table .column-header {
    -fx-background-color: #232323; /* 更深的黑灰色 */
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-border-color: transparent;
}

.record-table .table-cell {
    -fx-border-color: transparent;
    -fx-border-width: 0px;
    -fx-text-fill: white;
    -fx-alignment: CENTER; /* 添加居中属性 */
}

.record-table .table-row-cell .table-cell {
    -fx-border-color: transparent;
    -fx-border-width: 0px;
    -fx-text-fill: white; /* 白色字体 */
}

.record-table .table-row-cell {
    -fx-background-color: transparent;
    -fx-text-fill: white; /* 白色字体 */
}

.record-table .table-row-cell:selected {
    -fx-background-color: rgba(0, 206, 209, 0.2);
    -fx-text-fill: white; /* 选中行的白色字体 */
}

.record-table .table-row-cell:hover {
    -fx-background-color: rgba(255, 255, 255, 0.05);
    -fx-text-fill: white; /* 悬停行的白色字体 */
}

.record-table .column-header .label {
    -fx-text-fill: white;
}

/* 状态样式 - 保持原有颜色不变 */
.status-success {
    -fx-text-fill: #4CAF50;
    -fx-font-weight: bold;
}

.status-failed {
    -fx-text-fill: #f44336;
    -fx-font-weight: bold;
}

.status-processing {
    -fx-text-fill: #ff9800;
    -fx-font-weight: bold;
}


/* 分页按钮样式 */

.page-button {
    -fx-background-color: #404040;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 12px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.page-button:hover {
    -fx-background-color: #505050;
}

.page-button:disabled {
    -fx-background-color: #2c2c2c;
    -fx-text-fill: #666666;
    -fx-cursor: default;
}

.page-info {
    -fx-text-fill: #cccccc;
    -fx-font-size: 12px;
}

.close-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-size: 16px;
}

.close-button:hover {
    -fx-text-fill: #ff0000;
}

.empty-record-label,
.page-info-label {
    -fx-text-fill: white;
}


/* 动画关键帧定义 */

@keyframes fadeIn {
    from {
        -fx-opacity: 0;
        -fx-scale-x: 0.95;
        -fx-scale-y: 0.95;
    }
    to {
        -fx-opacity: 1;
        -fx-scale-x: 1;
        -fx-scale-y: 1;
    }
}

@keyframes fadeOut {
    from {
        -fx-opacity: 1;
        -fx-scale-x: 1;
        -fx-scale-y: 1;
    }
    to {
        -fx-opacity: 0;
        -fx-scale-x: 0.95;
        -fx-scale-y: 0.95;
    }
}
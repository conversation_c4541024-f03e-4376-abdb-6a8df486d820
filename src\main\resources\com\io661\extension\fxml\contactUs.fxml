<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.image.*?>
<ScrollPane xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21" stylesheets="@../css/contact-us.css" styleClass="contact-card" fitToWidth="true" fitToHeight="true" fx:controller="com.io661.extension.controller.ContactUsController">
      <VBox styleClass="box" alignment="BOTTOM_CENTER" fx:id="contactUsBox">
            <VBox styleClass="top">
                  <padding>
                        <Insets bottom="30.0" left="30.0" right="30.0" top="30.0" />
                  </padding>
                  <!-- 标题 -->
                  <Label styleClass="title-label" text="联系我们">
                        <font>
                              <Font size="24.0" />
                        </font>
                  </Label>
                  <VBox>
                        <Label styleClass="hint-label" text="如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：" />
                        <Label styleClass="help-label" text="• 添加客服微信" />
                        <VBox spacing="10">
                              <padding>
                                    <Insets left="30.0" />
                              </padding>
                              <ImageView fitHeight="124.0" fitWidth="124.0">
                                    <Image url="@../img/serviceVX.png" />
                              </ImageView>
                              <Label text="工作时间: 周一至周五 9:00-18:00" />
                        </VBox>
                        <Label styleClass="help-label" text="• 加入官方QQ群进行交流" />
                        <VBox spacing="10">
                              <padding>
                                    <Insets left="30.0" />
                              </padding>
                              <Label text="饰品玩家交流群" />
                              <HBox alignment="CENTER_LEFT" spacing="10">
                                    <ImageView fitHeight="124.0" fitWidth="124.0">
                                          <Image url="@../img/serviceQCode.png" />
                                    </ImageView>
                                    <VBox alignment="CENTER_LEFT">
                                          <Label text="群号：926091120" />
                                          <Label text="欢迎你的加入~" />
                                    </VBox>
                              </HBox>
                        </VBox>
                        <Label styleClass="help-label" text="• 访问官方网站查看最新公告" />
                        <Button fx:id="officialWebsite" mnemonicParsing="false" text="前往官网"
                                onAction="#GoWebsite" styleClass="contact-button" >
                              <padding>
                                    <Insets left="30.0" right="30.0" top="2.5" bottom="2.5" />
                              </padding>
                        </Button>
                  </VBox>
            </VBox>
            <!-- 使用 Region 占位符 -->
            <Region fx:id="fillRegion" styleClass="fill-region" />
            <VBox alignment="BOTTOM_CENTER" styleClass="bottom">
                  <!-- 分割线 -->
                  <Separator>
                        <padding>
                              <Insets left="10" right="10"/>
                        </padding>
                  </Separator>
                  <!-- 健康游戏忠告 -->
                  <HBox alignment="CENTER" styleClass="advise">
                        <Label text="抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。" />
                  </HBox>
                  <!-- 版本信息 -->
                  <HBox alignment="CENTER" styleClass="version-bottom">
                        <Label styleClass="help-label" text="V1.0.0"/>
                        <Label text="|" styleClass="split-line" />
                        <Label styleClass="help-label" text="2025-06-06" />
                        <Label text="|" styleClass="split-line" />
                        <Label styleClass="help-label" text="© 2025 IO661. All rights reserved." />
                  </HBox>
            </VBox>
      </VBox>
</ScrollPane>

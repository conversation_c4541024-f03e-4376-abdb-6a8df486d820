package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.User.*;
import com.io661.extension.service.WithdrawService;
import lombok.Data;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Data
public class WithdrawServiceImpl implements WithdrawService {
    private final CommonHttpUrl httpClient;

    public WithdrawServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }


    /**
     * 提现
     *
     * @param token 用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    @Override
    public WithdrawRes withdraw(WithdrawReq req, String token) {
        WithdrawRes res = new WithdrawRes();
        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return null;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            String endpoint = "web/withdraw";
            Gson gson = new Gson();
            String jsonBody = gson.toJson(req);

            String response = httpClient.doPost(endpoint, jsonBody, headers);

            return gson.fromJson(response, WithdrawRes.class);

        } catch (IOException e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 提现
     *
     * @param token 用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    @Override
    public WithdrawHistoryRes getWithdrawHistory(WithdrawHistoryReq req, String token) {
        WithdrawHistoryRes result = new WithdrawHistoryRes();

        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return null;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            // type 全部 0 成功 1 失败 2 处理中 3
            String endpoint = "web/withdraw?type=" + req.getType() + "&limit=" + req.getLimit() + "&startTime=" + req.getStartTime() + "&endTime=" + req.getEndTime() + "&page=" + req.getPage();

            String response = httpClient.doGet(endpoint, null, headers);

            Gson gson = new Gson();
            result = gson.fromJson(response, WithdrawHistoryRes.class);


            return result;
        } catch (Exception e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            return new WithdrawHistoryRes();
        }
    }

    @Override
    public DetailHistoryRes detailHistory(DetailHistoryReq req, String token) {
        DetailHistoryRes result = new DetailHistoryRes();

        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return null;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            // type 全部 0 充值提现 1 购买饰品 2 出售饰品 3 平台操作 4 其他 5
            String endpoint = "web/detail?type=" + req.getType() + "&limit=" + req.getLimit() + "&page=" + req.getPage() + "&search=" + req.getSearch();

            String response = httpClient.doGet(endpoint, null, headers);
            Gson gson = new Gson();
            result = gson.fromJson(response, DetailHistoryRes.class);


            return result;
        } catch (Exception e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            return new DetailHistoryRes();

        }
    }

}

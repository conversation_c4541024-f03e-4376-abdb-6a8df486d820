package com.io661.extension.controller;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.model.Steam.IO661TradeOfferRes;
import com.io661.extension.model.Steam.IO661TradeOrdersReq;
import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.service.Impl.AccountManagerServiceImpl;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.util.User.UserCookieManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.Data;

import java.io.IOException;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

@Data
public class IO661OrderListController implements Initializable {

    private final int pageSize = 15;
    // FXML 控件
    @FXML private ComboBox<String> orderTypeComboBox;
    @FXML private ComboBox<String> orderStatusComboBox;
    @FXML private ComboBox<String> steamAccountComboBox;
    @FXML private TextField searchField;
    @FXML private Button searchButton;
    @FXML private Button refreshButton;
    @FXML private Label orderSummaryLabel;
    @FXML private ScrollPane orderScrollPane;
    @FXML private VBox orderListContainer;
    @FXML private StackPane loadingPane;
    @FXML private VBox noDataPane;
    // 服务类
    private TransactionAssistantServiceImpl transactionAssistantService;
    private AccountManagerService accountManagerService;
    // 数据相关
    private List<IO661TradeOfferRes.ListItem> allOrders = new ArrayList<>();
    private List<SteamRes.SteamBind> steamAccounts = new ArrayList<>();
    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    // 查询参数
    private Long currentType = 2L; // 默认出售记录
    private String currentSearch = "";
    private String currentSteamId = "";
    private Long currenSubType = 0L; // 默认全部状态

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 初始化服务
        transactionAssistantService = new TransactionAssistantServiceImpl();
        accountManagerService = new AccountManagerServiceImpl();

        // 初始化界面
        initializeUI();

        // 设置事件处理
        setupEventHandlers();

        // 加载Steam账号列表
        loadSteamAccounts();

        // 初始加载订单数据
        refreshOrders();
    }

    /**
     * 初始化界面
     */
    private void initializeUI() {
        // 初始化订单类型下拉框
        ObservableList<String> orderTypeItems = FXCollections.observableArrayList();
        orderTypeItems.addAll( "出售记录", "购买记录");
        orderTypeComboBox.setItems(orderTypeItems);
        orderTypeComboBox.getSelectionModel().select(0); // 默认选择"出售记录"

        // 初始化订单状态下拉框
        ObservableList<String> orderStatusItems = FXCollections.observableArrayList();
        orderStatusItems.addAll("全部状态", "等待卖家发送报价", "交易搁置", "等待买家确认报价", "交易成功", "交易失败");
        orderStatusComboBox.setItems(orderStatusItems);
        orderStatusComboBox.getSelectionModel().select(0); // 默认选择"全部状态"

        // 设置滚动监听器实现上拉加载
        orderScrollPane.vvalueProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue.doubleValue() >= 0.95 && !isLoading && hasMoreData) {
                loadMoreOrders();
            }
        });
    }

    /**
     * 设置事件处理
     */
    private void setupEventHandlers() {
        // 订单类型选择事件
        orderTypeComboBox.setOnAction(event -> {
            int selectedIndex = orderTypeComboBox.getSelectionModel().getSelectedIndex();
            switch (selectedIndex) {
                case 0: // 出售记录
                    currentType = 2L;
                    break;
                case 1: // 购买记录
                    currentType = 1L;
                    break;
                default: // 全部
                    currentType = 2L; // 默认显示出售记录
                    break;
            }
            refreshOrders();
        });

        // 订单状态选择事件 "全部状态", "等待卖家发送报价", "交易搁置", "等待买家确认报价", "交易成功", "交易失败"
        orderStatusComboBox.setOnAction(event -> {
            int selectedIndex = orderStatusComboBox.getSelectionModel().getSelectedIndex();
            switch (selectedIndex) {
                // 全部订单
                case 0:
                    currenSubType = 0L;
                    break;
                // 等待卖家发送报价
                case 1:
                    currenSubType = 2L;
                    break;
                // 交易搁置
                case 2:
                    currenSubType = 3L;
                    break;
                // 等待买家确认报价
                case 3:
                    currenSubType = 4L;
                    break;
                // 交易成功
                case 4:
                    currenSubType = 5L;
                    break;
                // 交易失败
                case 5:
                    currenSubType = 6L;
                    break;
                // 默认状态
                default:
                    currenSubType = 0L;
                    break;
            }
            refreshOrders();
        });

        // Steam账号选择事件
        steamAccountComboBox.setOnAction(event -> {
            String selectedAccount = steamAccountComboBox.getSelectionModel().getSelectedItem();
            if (selectedAccount != null && !selectedAccount.equals("全部账号")) {
                // 从选择的文本中提取steamId
                for (SteamRes.SteamBind account : steamAccounts) {
                    if (selectedAccount.contains(account.getSteamId())) {
                        currentSteamId = account.getSteamId();
                        break;
                    }
                }
            } else {
                currentSteamId = "";
            }
            refreshOrders();
        });

        // 搜索按钮事件
        searchButton.setOnAction(event -> performSearch());

        // 搜索框回车事件
        searchField.setOnAction(event -> performSearch());

        // 刷新按钮事件
        refreshButton.setOnAction(event -> refreshOrders());
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        currentSearch = searchField.getText().trim();
        refreshOrders();
    }

    /**
     * 加载Steam账号列表
     */
    private void loadSteamAccounts() {
        CompletableFuture.supplyAsync(() -> {
            try {
                String token = UserCookieManager.readToken();
                if (token != null && !token.isEmpty()) {
                    return accountManagerService.getAllSteamAccount(token);
                }
            } catch (Exception e) {
                System.err.println("加载Steam账号失败: " + e.getMessage());
            }
            return new ArrayList<SteamRes.SteamBind>();
        }).thenAccept(accounts -> {
            Platform.runLater(() -> {
                steamAccounts.clear();
                steamAccounts.addAll(accounts);

                ObservableList<String> accountItems = FXCollections.observableArrayList();
                accountItems.add("全部账号");

                for (SteamRes.SteamBind account : accounts) {
                    String displayText = String.format("%s (%s)",
                            account.getNickname() != null ? account.getNickname() : "未知用户",
                            account.getSteamId());
                    accountItems.add(displayText);
                }

                steamAccountComboBox.setItems(accountItems);
                steamAccountComboBox.getSelectionModel().select(0);
            });
        });
    }

    private void refreshOrders() {
        currentPage = 1;
        hasMoreData = true;
        allOrders.clear();
        orderListContainer.getChildren().clear();
        loadOrders();
    }

    /**
     * 加载更多订单(动态加载)
     */
    private void loadMoreOrders() {
        if (!isLoading && hasMoreData) {
            currentPage++;
            loadOrders();
        }
    }

    /**
     * 动态加载订单逻辑
     */
    private void loadOrders() {
        if (isLoading) return;

        isLoading = true;
        showLoading(true);

        CompletableFuture.supplyAsync(() -> {
            try {
                IO661TradeOrdersReq request = new IO661TradeOrdersReq();
                request.setPage((long) currentPage);
                request.setLimit((long) pageSize);
                request.setType(currentType);
                request.setSubType(currenSubType);
                request.setSearch(currentSearch);
                request.setSteamId(currentSteamId);

                return transactionAssistantService.getAllOrders(request);
            } catch (Exception e) {
                System.err.println("加载订单失败: " + e.getMessage());
                return new IO661TradeOfferRes();
            }
        }).thenAccept(response -> {
            Platform.runLater(() -> {
                isLoading = false;
                showLoading(false);

                if (response != null && response.getCode() == 0 && response.getData() != null) {
                    List<IO661TradeOfferRes.ListItem> newOrders = response.getData().getList();

                    if (newOrders != null && !newOrders.isEmpty()) {
                        allOrders.addAll(newOrders);
                        addOrdersToUI(newOrders);

                        // 检查是否还有更多数据
                        hasMoreData = newOrders.size() >= pageSize;
                    } else {
                        hasMoreData = false;
                    }

                    updateSummaryLabel();
                    updateNoDataVisibility();
                } else {
                    hasMoreData = false;
                    updateNoDataVisibility();
                }
            });
        });
    }

    private void addOrdersToUI(List<IO661TradeOfferRes.ListItem> orders) {
        for (IO661TradeOfferRes.ListItem order : orders) {
            VBox orderCard = createOrderCard(order);
            orderListContainer.getChildren().add(orderCard);
        }
    }

    /**
     * 订单详情展开
     */
    private VBox createOrderCard(IO661TradeOfferRes.ListItem order) {
        VBox card = new VBox();
        card.getStyleClass().add("order-card");
        card.setPadding(new Insets(15));

        // 主要内容区域 - 单行布局
        HBox mainContent = new HBox(15);
        mainContent.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // 1. 订单信息列 - 订单号和时间 (28%)
        VBox orderInfoCol = new VBox(3);
        orderInfoCol.setPrefWidth(280);
        orderInfoCol.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label orderNoLabel = new Label("订单号：" + (order.getOrderNo() != null ? order.getOrderNo() : "未知"));
        orderNoLabel.getStyleClass().add("order-no-label");

        Label timeLabel = new Label(order.getCreateTime() != null ? order.getCreateTime() : "未知时间");
        timeLabel.getStyleClass().add("time-label");

        orderInfoCol.getChildren().addAll(orderNoLabel, timeLabel);

        // 2. 物品列 - 图片和名称 (28%)
        HBox itemCol = new HBox(10);
        itemCol.setPrefWidth(280);
        itemCol.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // 物品图片
        ImageView itemImage = new ImageView();
        itemImage.setFitWidth(60);
        itemImage.setFitHeight(60);
        itemImage.setPreserveRatio(true);
        itemImage.getStyleClass().add("item-image");

        if (order.getIconUrl() != null && !order.getIconUrl().isEmpty()) {
            try {
                itemImage.setImage(new Image(order.getIconUrl(), true));
            } catch (Exception e) {
                itemImage.setImage(new Image("/com/io661/extension/img/Logo.png"));
            }
        } else {
            itemImage.setImage(new Image("/com/io661/extension/img/Logo.png"));
        }

        // 物品信息
        VBox itemInfo = new VBox(3);
        Label itemNameLabel = new Label(order.getItemName() != null ? order.getItemName() : "未知物品");
        itemNameLabel.getStyleClass().add("item-name-label");
        itemNameLabel.setWrapText(true);
        itemNameLabel.setMaxWidth(220);

        // 添加磨损值信息（如果有）
        if (order.getFloatValue() != null && order.getFloatValue() > 0) {
            Label floatLabel = new Label("磨损值: " + String.format("%.6f", order.getFloatValue()));
            floatLabel.getStyleClass().add("float-label");
            itemInfo.getChildren().addAll(itemNameLabel, floatLabel);
        } else {
            itemInfo.getChildren().add(itemNameLabel);
        }

        itemCol.getChildren().addAll(itemImage, itemInfo);

        // 3. 交易类型列 (7%)
        VBox tradeTypeCol = new VBox();
        tradeTypeCol.setPrefWidth(70);
        tradeTypeCol.setAlignment(javafx.geometry.Pos.CENTER);

        Label tradeTypeLabel = new Label(currentType == 1L ? "购买" : "出售");
        tradeTypeLabel.getStyleClass().add("trade-type-label");
        tradeTypeCol.getChildren().add(tradeTypeLabel);

        // 4. 实际收入列 (15%)
        VBox incomeCol = new VBox(3);
        incomeCol.setPrefWidth(150);
        incomeCol.setAlignment(javafx.geometry.Pos.CENTER);

        DecimalFormat df = new DecimalFormat("#0.00");
        // 将分转换为元
        double priceInYuan = order.getPrice() != null ? order.getPrice() / 100.0 : 0.0;
        double terminalPriceInYuan = order.getTerminalPrice() != null ? order.getTerminalPrice() / 100.0 : 0.0;

        Label priceLabel = new Label("¥" + df.format(priceInYuan));
        priceLabel.getStyleClass().add("price-label");

        Label terminalPriceLabel = new Label("¥" + df.format(terminalPriceInYuan));
        terminalPriceLabel.getStyleClass().add("terminal-price-label");

        incomeCol.getChildren().addAll(priceLabel, terminalPriceLabel);

        // 5. 状态列 (15%)
        VBox statusCol = new VBox();
        statusCol.setPrefWidth(150);
        statusCol.setAlignment(javafx.geometry.Pos.CENTER);

        Label statusLabel = new Label(order.getStatusName() != null ? order.getStatusName() : "未知状态");
        statusLabel.getStyleClass().addAll("status-label", getStatusStyleClass(order.getStatus()));
        statusCol.getChildren().add(statusLabel);

        // 6. 弹性空间 - 将操作按钮推到最右边
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        // 6. 弹性空间 - 将操作按钮推到最右边
        Region spacer2 = new Region();
        spacer2.setPrefWidth(120);
        HBox.setHgrow(spacer2, Priority.ALWAYS);

        // 7. 操作列 - 右对齐 (12%)
        VBox actionCol = new VBox();
        actionCol.setPrefWidth(120);
        actionCol.setAlignment(javafx.geometry.Pos.CENTER);

        Button detailButton = new Button("查看详情");
        detailButton.getStyleClass().add("detail-button");
        detailButton.setOnAction(e -> showOrderDetail(order.getOrderNo()));
        actionCol.getChildren().add(detailButton);

        // 组装主要内容 购买记录不展示订单详情
        if (currentType != 2L){
            mainContent.getChildren().addAll(orderInfoCol, itemCol, tradeTypeCol, incomeCol, statusCol, spacer, spacer2);
        }
        else {
            mainContent.getChildren().addAll(orderInfoCol, itemCol, tradeTypeCol, incomeCol, statusCol, spacer, actionCol);
        }

        card.getChildren().add(mainContent);
        return card;
    }

    private String getStatusStyleClass(int status) {
        return switch (status) {
            case 21 -> "status-waiting";
            case 22 -> "status-pending";
            case 23 -> "status-confirming";
            case 30 -> "status-success";
            case 40 -> "status-failed";
            default -> "status-unknown";
        };
    }

    private void showOrderDetail(String orderNo) {
        if (orderNo == null || orderNo.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR,"错误", "订单号不能为空");
            return;
        }

        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/order-detail-dialog.fxml"));
            Parent root = loader.load();

            OrderDetailDialogController controller = loader.getController();
            controller.setOrderNo(orderNo);
            controller.loadOrderDetail();

            Stage stage = new Stage();
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.initStyle(StageStyle.DECORATED);
            stage.setTitle("订单详情 - " + orderNo);
            stage.setScene(new Scene(root));
            stage.setResizable(false);

            // 设置关闭按钮事件
            controller.setStage(stage);

            stage.showAndWait();

        } catch (IOException e) {
            System.err.println("打开订单详情窗口失败: " + e.getMessage());
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR,"错误", "无法打开订单详情窗口");
        }
    }

    private void showLoading(boolean show) {
        loadingPane.setVisible(show);
    }

    private void updateSummaryLabel() {
        Platform.runLater(() -> {
            orderSummaryLabel.setText("共 " + allOrders.size() + " 个订单");
        });
    }

    private void updateNoDataVisibility() {
        Platform.runLater(() -> {
            boolean hasData = !allOrders.isEmpty();
            noDataPane.setVisible(!hasData);
            orderScrollPane.setVisible(hasData);
        });
    }
}

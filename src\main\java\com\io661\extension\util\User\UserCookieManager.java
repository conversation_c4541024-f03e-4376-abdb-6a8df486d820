package com.io661.extension.util.User;

import com.io661.extension.util.SQLite.SqliteManager;



/**
 * 用户cookie管理
 */
public class UserCookieManager {

    /**
     * 保存授权令牌到本地文件
     *
     * @param token 授权令牌
     * @return 是否保存成功
     */
    public static boolean saveToken(String token) {
        if (token == null) {
            System.err.println("Cookie 值不能为 null，无法保存。");
            return false;
        }
        boolean success = SqliteManager.upsertUserCookie(token);
        if (success) {
            System.out.println("cookie已更新");
        } else {
            System.err.println("cookie保存到数据库失败。");
        }
        return success;

    }

    /**
     * 从本地文件读取授权令牌
     *
     * @return 授权令牌，如果不存在则返回null
     */
    public static String readToken() {
        String cookie = SqliteManager.queryUserCookie();
        if (cookie == null || cookie.isEmpty()) {
            System.out.println("未找到有效的cookie");
        }
        return cookie;
    }

    /**
     * 删除本地保存的授权令牌
     *
     * @return 是否删除成功
     */
    public static boolean deleteToken() {
        boolean success = SqliteManager.deleteUserCookie();
        if (success) {
            System.out.println("cookie已删除");
        }
        return success;
    }
    /**
     * 检查令牌文件是否存在
     *
     * @return 如果令牌文件存在且可读返回true，否则返回false
     */
    public static boolean tokenFileExists() {
        try {
            String cookie = SqliteManager.queryUserCookie();
            return cookie != null && !cookie.trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
}

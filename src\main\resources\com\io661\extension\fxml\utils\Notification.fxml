<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.*?>
<StackPane xmlns:fx="http://javafx.com/fxml/1" fx:id="root" stylesheets="@../../css/utils/Notification.css" xmlns="http://javafx.com/javafx">
    <children>
        <HBox fx:id="hBox" styleClass="el-notification">
            <children>
                <VBox fx:id="vBox" styleClass="el-notification__group" HBox.hgrow="ALWAYS">
                    <children>
                        <HBox fx:id="titleHbox" />
                        <StackPane fx:id="content" alignment="CENTER_LEFT" VBox.vgrow="ALWAYS">
                            <children>
                                <Label fx:id="messageLabel" styleClass="el-notification__content" text="消息提示" wrapText="true" />
                            </children>
                        </StackPane>
                    </children>
                    <HBox.margin>
                        <Insets left="13.0" right="8.0" />
                    </HBox.margin>
                </VBox>
            </children>
        </HBox>
    </children>
</StackPane>

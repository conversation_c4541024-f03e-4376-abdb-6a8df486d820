package com.io661.extension.controller;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.model.Steam.ChangeOnSellStatusReq;
import com.io661.extension.model.Steam.ChangeOnSellStatusRes;
import com.io661.extension.model.Steam.SteamInventoryResponse;
import com.io661.extension.model.YouPin.YouPinUserInventoryOnSellDataListRes;
import com.io661.extension.model.YouPin.YouPinUserItemsOnSellPriceChangeRes;
import com.io661.extension.model.YouPin.YouPinUserSellInventoryReq;
import com.io661.extension.model.YouPin.YouPinUserSellInventoryRes;
import com.io661.extension.service.Impl.PersonalSettingServiceImpl;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.service.PersonalSettingService;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.service.YouPinService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import lombok.Data;
import lombok.Setter;

import java.net.URL;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 市场上架对话框控制器
 */
@Data
public class MarketListingDialogController implements Initializable {
    // 默认图标（资源路径）
    private static final String DEFAULT_ICON = "/com/io661/extension/img/Logo.png";
    // 自定义CSS路径
    private static final String ALERT_CSS = "/com/io661/extension/css/alert.css";
    @FXML
    public Button oneClickPricingButton;
    @FXML
    public ComboBox<String> referencePriceComboBox;
    private Double currentOneClickOnSaleRatio = 1.00;
    private Double currentOneClickOnSaleRatioCustomize = 1.00;
    @FXML
    private Button closeButton;

    @FXML
    private Button cancelButton;

    @FXML
    private Button confirmButton;

    @FXML
    private CheckBox io661Checkbox;

    @FXML
    private CheckBox uuCheckbox;

    @FXML
    private CheckBox buffCheckbox;

    @FXML
    private Label priceHelpLabel;

    // 移除全局价格输入框，改为每个物品单独设置价格

    @FXML
    private VBox itemsContainer;

    private List<SteamInventoryResponse.InventoryDataList> selectedItems = new ArrayList<>();
    private TransactionAssistantService transactionAssistantService;
    private YouPinService youPinService;
    private PersonalSettingService personalSettingService;

    // 是否是价格修改模式
    private boolean isPriceModifyMode = false;
    /**
     * -- SETTER --
     *  设置当前Steam账号ID
     */
    // 当前选中的Steam账号ID
    @Setter
    private String currentSteamId;

    /**
     * 设置要上架或修改价格的物品
     *
     * @param items 物品列表
     * @param isPriceModify 是否是价格修改模式
     * @param confirmButtonText 确认按钮文本
     */
    public void setItems(List<SteamInventoryResponse.InventoryDataList> items, boolean isPriceModify, String confirmButtonText) {
        this.selectedItems = items;
        this.isPriceModifyMode = isPriceModify;

        // 设置确认按钮文本
        if (confirmButton != null && confirmButtonText != null && !confirmButtonText.isEmpty()) {
            confirmButton.setText(confirmButtonText);
        }

        // 清空物品容器
        itemsContainer.getChildren().clear();

        // 添加物品到容器
        for (SteamInventoryResponse.InventoryDataList item : items) {
            HBox itemRow = createItemRow(item);
            itemsContainer.getChildren().add(itemRow);
        }
    }

    /**
     * 设置要上架的物品（向后兼容）
     *
     * @param items 物品列表
     */
    public void setItems(List<SteamInventoryResponse.InventoryDataList> items) {
        setItems(items, false, "确定上架");
    }

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 初始化服务
        transactionAssistantService = new TransactionAssistantServiceImpl();
        personalSettingService = new PersonalSettingServiceImpl();
        youPinService = new YouPinServiceImpl();


        // 初始化订单状态下拉框
        ObservableList<String> oneClickOnSaleRatio = FXCollections.observableArrayList();
        oneClickOnSaleRatio.addAll("参考价100%定价", "参考价110%定价", "参考价101%定价", "参考价99.5%定价", "参考价99%定价", "自定义规则(0.97~1.1)");
        referencePriceComboBox.setItems(oneClickOnSaleRatio);
        referencePriceComboBox.getSelectionModel().select(0); // 默认选择"参考价100%定价"

        handleOneClickOnSaleRatio();

        // 设置确认按钮启用状态
        confirmButton.setDisable(false);
    }

    /**
     * 创建物品行
     */
    private HBox createItemRow(SteamInventoryResponse.InventoryDataList item) {
        HBox itemRow = new HBox();
        itemRow.setAlignment(Pos.CENTER_LEFT);
        itemRow.setSpacing(10);
        itemRow.setPadding(new Insets(5));
        itemRow.getStyleClass().add("item-row");

        // 物品图片
        ImageView itemImage = new ImageView();
        itemImage.setFitHeight(40);
        itemImage.setFitWidth(40);
        itemImage.setPreserveRatio(true);
        itemImage.getStyleClass().add("item-image");

        // 设置图片URL
        if (item.getIconUrl() != null && !item.getIconUrl().isEmpty()) {
            try {
                Image image = new Image(item.getIconUrl(), true);
                itemImage.setImage(image);
            } catch (Exception e) {
                System.out.println("图片加载异常: " + e.getMessage());
            }
        }

        // 物品名称和价格容器
        VBox itemInfo = new VBox();
        itemInfo.setSpacing(2);
        HBox.setHgrow(itemInfo, Priority.ALWAYS);

        // 物品名称
        Label nameLabel = new Label(item.getItemName() != null ? item.getItemName() : item.getHashName());
        nameLabel.getStyleClass().add("item-name");

        // 显示物品数量（如果是合并物品）
        Label quantityLabel = new Label();
        if (item.getQuantity() != null && item.getQuantity() > 1) {
            quantityLabel.setText("共 " + item.getQuantity() + " 件");
            quantityLabel.getStyleClass().add("item-quantity");
        } else {
            quantityLabel.setText("共 1 件");
            quantityLabel.getStyleClass().add("item-quantity");
        }

        // 参考价格
        Label priceLabel = new Label();
        if (item.getRefPrice() != null) {
            double priceInYuan = item.getRefPrice() / 100.0;
            DecimalFormat df = new DecimalFormat("0.00");
            priceLabel.setText("参考价: ¥" + df.format(priceInYuan));
        } else {
            priceLabel.setText("参考价: 暂无");
        }
        priceLabel.getStyleClass().add("item-price");

        itemInfo.getChildren().addAll(nameLabel, quantityLabel, priceLabel);

        // 创建价格输入区域
        VBox priceInputContainer = new VBox();
        priceInputContainer.setSpacing(5);

        // IO661价格输入区域
        HBox io661PriceContainer = new HBox();
        io661PriceContainer.setAlignment(Pos.CENTER_RIGHT);
        io661PriceContainer.setSpacing(5);

        Label io661ReceivedPriceLabel = new Label("IO661到手价:");
        io661ReceivedPriceLabel.getStyleClass().add("sell-price-label");

        TextField io661ReceivedPriceField = new TextField();
        io661ReceivedPriceField.getStyleClass().add("price-field");
        io661ReceivedPriceField.setPrefWidth(80);
        io661ReceivedPriceField.setPromptText("期望到手价");

        io661PriceContainer.getChildren().addAll(io661ReceivedPriceLabel, io661ReceivedPriceField);

        // 创建隐藏的上架价字段用于内部计算
        TextField io661ListingPriceField = new TextField();
        io661ListingPriceField.setVisible(false);
        io661ListingPriceField.setManaged(false);

        // 悠悠有品价格输入区域（默认隐藏）
        HBox uuPriceContainer = new HBox();
        uuPriceContainer.setAlignment(Pos.CENTER_RIGHT);
        uuPriceContainer.setSpacing(5);
        uuPriceContainer.setVisible(false);
        uuPriceContainer.setManaged(false);

        Label uuReceivedPriceLabel = new Label("UU到手价:");
        uuReceivedPriceLabel.getStyleClass().add("sell-price-label");

        TextField uuReceivedPriceField = new TextField();
        uuReceivedPriceField.getStyleClass().add("price-field");
        uuReceivedPriceField.setPrefWidth(80);
        uuReceivedPriceField.setPromptText("期望到手价");

        uuPriceContainer.getChildren().addAll(uuReceivedPriceLabel, uuReceivedPriceField);

        // 创建隐藏的上架价字段用于内部计算
        TextField uuListingPriceField = new TextField();
        uuListingPriceField.setVisible(false);
        uuListingPriceField.setManaged(false);

        priceInputContainer.getChildren().addAll(io661PriceContainer, uuPriceContainer);

        // 监听UU复选框状态变化
//        uuCheckbox.selectedProperty().addListener((observable, oldValue, newValue) -> {
//            uuPriceContainer.setVisible(newValue);
//            uuPriceContainer.setManaged(newValue);
//        });

        // 设置默认价格
        DecimalFormat df = new DecimalFormat("0.00");

        // 为IO661价格输入框设置默认价格（基于到手价）
        if (isPriceModifyMode && item.isOnSell() && item.getOnSellPrice() != null && item.getOnSellPrice() > 0) {
            // 修改价格模式：显示当前到手价
            int currentReceivedPrice = personalSettingService.calculateReceivedPrice("io661", item.getOnSellPrice());
            double receivedPriceInYuan = currentReceivedPrice / 100.0;
            io661ReceivedPriceField.setText(df.format(receivedPriceInYuan));
            uuReceivedPriceField.setText(df.format(receivedPriceInYuan));
        } else if (item.getRefPrice() != null) {
            // 上架模式：使用参考价格作为期望到手价
            double refPriceInYuan = item.getRefPrice() / 100.0;
            io661ReceivedPriceField.setText(df.format(refPriceInYuan));
            uuReceivedPriceField.setText(df.format(refPriceInYuan));
        }

        // 设置价格计算事件
        setupPriceCalculation(io661ReceivedPriceField, io661ListingPriceField, "io661", item, df);
//        setupPriceCalculation(uuReceivedPriceField, uuListingPriceField, "youpin", item, df);

        // 将价格输入框存储在物品的用户数据中，以便后续获取
        // 存储一个Map，包含各平台的到手价和上架价输入框
        Map<String, Map<String, TextField>> priceFields = new HashMap<>();
        Map<String, TextField> io661Fields = new HashMap<>();
        io661Fields.put("received", io661ReceivedPriceField);
        io661Fields.put("listing", io661ListingPriceField);
        Map<String, TextField> uuFields = new HashMap<>();
        uuFields.put("received", uuReceivedPriceField);
        uuFields.put("listing", uuListingPriceField);
        priceFields.put("io661", io661Fields);
        priceFields.put("uu", uuFields);
        itemRow.setUserData(priceFields);

        // 添加到行
        itemRow.getChildren().addAll(itemImage, itemInfo, priceInputContainer);

        return itemRow;
    }

    /**
     * 设置价格计算事件处理
     */
    private void setupPriceCalculation(TextField receivedPriceField, TextField listingPriceField, String platform, SteamInventoryResponse.InventoryDataList item, DecimalFormat df) {
        // 初始化设置默认值为参考价格
        double refPriceYuan = item.getRefPrice() / 100.0;
        receivedPriceField.setText(df.format(refPriceYuan));

        // 移除全选事件监听器，允许自由选择光标位置
        // receivedPriceField.setOnMouseClicked(event -> receivedPriceField.selectAll());

        // 使用TextFormatter替代文本监听器，提供更好的输入控制
        TextFormatter<String> textFormatter = new TextFormatter<>(change -> {
            String newText = change.getControlNewText();

            // 允许空输入（用户正在删除内容）
            if (newText.isEmpty()) {
                return change;
            }

            // 验证数字格式（最多两位小数）
            if (!newText.matches("\\d*(\\.\\d{0,2})?")) {
                return null; // 拒绝非法输入
            }

            // 验证数值范围
            try {
                double inputValue = Double.parseDouble(newText);
                double maxAllowed = refPriceYuan * 3;

                if (inputValue > maxAllowed) {
                    // 设置上限值但允许继续输入
                    if (!change.isContentChange()) {
                        receivedPriceField.setText(df.format(maxAllowed));
                        receivedPriceField.positionCaret(receivedPriceField.getText().length());
                        return null;
                    }
                }
            } catch (NumberFormatException ignored) {
                // 格式正确但可能超出范围，由后续处理
            }

            return change;
        });

        receivedPriceField.setTextFormatter(textFormatter);

        // 到手价变化时自动计算上架价
        receivedPriceField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue == null || newValue.isEmpty()) {
                listingPriceField.setText("");
                return;
            }

            try {
                double receivedPrice = Double.parseDouble(newValue);
                double maxAllowed = refPriceYuan * 3;

                // 确保价格在有效范围内
                if (receivedPrice <= 0) {
                    receivedPrice = refPriceYuan;
                    receivedPriceField.setText(df.format(receivedPrice));
                } else if (receivedPrice > maxAllowed) {
                    receivedPrice = maxAllowed;
                    receivedPriceField.setText(df.format(receivedPrice));
                }

                int receivedPriceInCents = (int) (receivedPrice * 100);
                int listingPriceInCents = personalSettingService.calculateListingPrice(platform, receivedPriceInCents);
                double listingPriceInYuan = listingPriceInCents / 100.0;
                listingPriceField.setText(df.format(listingPriceInYuan));
            } catch (NumberFormatException e) {
                listingPriceField.setText("");
            }
        });

        // 焦点事件处理 - 只在失去焦点时进行最终验证
        receivedPriceField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue) { // 失去焦点时
                String text = receivedPriceField.getText();
                if (text == null || text.isEmpty()) {
                    receivedPriceField.setText(df.format(refPriceYuan));
                    return;
                }

                try {
                    double price = Double.parseDouble(text);
                    // 应用最终价格限制规则
                    if (price <= 0) {
                        receivedPriceField.setText(df.format(refPriceYuan));
                    } else if (price > refPriceYuan * 3) {
                        receivedPriceField.setText(df.format(refPriceYuan * 3));
                    } else {
                        // 仅格式化，不改变值
                        receivedPriceField.setText(df.format(price));
                    }
                } catch (NumberFormatException e) {
                    receivedPriceField.setText(df.format(refPriceYuan));
                }
            }
        });

        // 添加回车键处理
        receivedPriceField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                // 触发失去焦点事件
                listingPriceField.requestFocus();
            }
        });
    }

    /**
     * 处理关闭按钮点击
     */
    @FXML
    private void handleClose() {
        closeDialog();
    }

    /**
     * 处理取消按钮点击
     */
    @FXML
    private void handleCancel() {
        closeDialog();
    }

    /**
     * 设置价格输入框的事件处理（保留原方法以兼容）
     */
    private void setupPriceField(TextField priceField, SteamInventoryResponse.InventoryDataList item, DecimalFormat df) {
        // 触发一次焦点事件，确保用户输入的价格被记录
        priceField.setOnMouseClicked(event -> {
            priceField.selectAll();
        });

        // 设置价格输入框只允许输入数字和小数点，且限制小数点后两位
        priceField.textProperty().addListener((observable, oldValue, newValue) -> {
            // 检查是否是有效的价格格式（数字和小数点）
            if (!newValue.matches("\\d*(\\.\\d{0,2})?")) {
                // 如果不是有效格式，回退到上一个值
                priceField.setText(oldValue);
            }
        });

        // 设置焦点事件，当获得焦点时选中所有文本，方便用户直接输入新价格
        priceField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) { // 获得焦点
                Platform.runLater(priceField::selectAll);
            } else { // 失去焦点时格式化为两位小数
                try {
                    if (!priceField.getText().isEmpty()) {
                        double price = Double.parseDouble(priceField.getText());
                        // 确保价格大于0
                        if (price > 0) {
                            priceField.setText(df.format(price));
                        } else {
                            // 如果价格小于等于0，设置为默认参考价格
                            if (item.getRefPrice() != null) {
                                double defaultPrice = item.getRefPrice() / 100.0;
                                priceField.setText(df.format(defaultPrice));
                            } else {
                                priceField.setText("0.01"); // 最小价格
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    // 如果解析失败，设置为默认参考价格
                    if (item.getRefPrice() != null) {
                        double defaultPrice = item.getRefPrice() / 100.0;
                        priceField.setText(df.format(defaultPrice));
                    } else {
                        priceField.setText("0.01"); // 最小价格
                    }
                }
            }
        });
    }

    /**
     * 处理确认按钮点击
     */
    @FXML
    private void handleConfirm() {
        // 获取YouPin token，如果没有currentSteamId或读取失败则为null
//        String youPinToken = null;
//        if (currentSteamId != null && !currentSteamId.isEmpty()) {
//            try {
//                youPinToken = YouPinCookieManager.readYouPinCookie(currentSteamId);
//            } catch (Exception e) {
//                System.out.println("读取YouPin Cookie失败: " + e.getMessage());
//            }
//        }

        // 如果没有currentSteamId，尝试从第一个物品获取steamId
//        if (currentSteamId == null || currentSteamId.isEmpty()) {
//            if (!selectedItems.isEmpty() && selectedItems.getFirst().getSteamId() != null) {
//                currentSteamId = selectedItems.getFirst().getSteamId();
//                System.out.println("从物品获取steamId: " + currentSteamId);
//                try {
//                    youPinToken = YouPinCookieManager.readYouPinCookie(currentSteamId);
//                } catch (Exception e) {
//                    System.out.println("读取YouPin Cookie失败: " + e.getMessage());
//                }
//            }
//        }

//        final String token = youPinToken;
        try {
            // 创建上架/修改价格请求
            ChangeOnSellStatusReq request = new ChangeOnSellStatusReq();
            List<ChangeOnSellStatusReq.Inventory> inventoryList = new ArrayList<>();

//            YouPinUserSellInventoryReq inventoryYouPinList = new YouPinUserSellInventoryReq();


            if (isPriceModifyMode) {
                System.out.println("准备修改 " + selectedItems.size() + " 个物品的价格");
                System.out.println("注意：将使用用户输入的新价格，更新在售物品价格");
            } else {
                System.out.println("准备上架 " + selectedItems.size() + " 个物品");
                System.out.println("注意：将使用用户输入的价格，而非参考价格");
            }

            // 遍历物品容器中的所有行
            for (int i = 0; i < itemsContainer.getChildren().size(); i++) {
                HBox itemRow = (HBox) itemsContainer.getChildren().get(i);
                SteamInventoryResponse.InventoryDataList item = selectedItems.get(i);
                String itemName = item.getItemName() != null ? item.getItemName() : item.getHashName();

                // 获取该行的价格输入框Map
                @SuppressWarnings("unchecked")
                Map<String, Map<String, TextField>> priceFields = (Map<String, Map<String, TextField>>) itemRow.getUserData();
                Map<String, TextField> io661Fields = priceFields.get("io661");
                Map<String, TextField> uuFields = priceFields.get("uu");

                TextField io661ListingPriceField = io661Fields.get("listing");
                TextField uuListingPriceField = uuFields.get("listing");

                String io661Price = io661ListingPriceField.getText();
                String io661PriceText = io661ListingPriceField.getText() == null || io661ListingPriceField.getText().isEmpty() || io661ListingPriceField.getText().isBlank() ? (item.getRefPrice() == null ? "0.00" : String.valueOf(item.getRefPrice() / 100.0)) : io661ListingPriceField.getText();
                String uuPriceText = uuListingPriceField.getText();

                // 验证IO661价格
                if (io661PriceText.isEmpty()) {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "价格错误", "请为所有物品设置IO661到手价");
                    return;
                }

                // 验证悠悠有品价格（如果选中了悠悠有品）
//                if (uuCheckbox.isSelected() && (uuPriceText == null || uuPriceText.isEmpty())) {
//                    CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "价格错误", "请为所有物品设置悠悠有品到手价");
//                    return;
//                }

                try {
                    // 处理IO661价格
                    double io661PriceInYuan = Double.parseDouble(io661PriceText);


                    // 验证IO661价格是否大于0
                    if (io661PriceInYuan <= 0) {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的IO661价格必须大于0");
                        return;
                    }

                    // 处理悠悠有品价格
                    double uuPriceInYuan = 0;
//                    if (uuCheckbox.isSelected()) {
//                        uuPriceInYuan = Double.parseDouble(uuPriceText);
//                        if (uuPriceInYuan <= 0) {
//                            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的悠悠有品价格必须大于0");
//                            return;
//                        }
//                    }

                    // 验证价格格式并格式化
                    DecimalFormat df = new DecimalFormat("0.00");
                    String[] io661Parts = io661PriceText.split("\\.");
                    if (io661Parts.length > 1 && io661Parts[1].length() > 2) {
                        io661PriceInYuan = Double.parseDouble(df.format(io661PriceInYuan));
                        io661PriceText = df.format(io661PriceInYuan);
                        io661ListingPriceField.setText(io661PriceText);
                    }

//                    if (uuCheckbox.isSelected()) {
//                        String[] uuParts = uuPriceText.split("\\.");
//                        if (uuParts.length > 1 && uuParts[1].length() > 2) {
//                            uuPriceInYuan = Double.parseDouble(df.format(uuPriceInYuan));
//                            uuPriceText = df.format(uuPriceInYuan);
//                            uuListingPriceField.setText(uuPriceText);
//                        }
//                    }

                    // IO661价格转换为分（Integer类型）

                    int io661PriceInCents = personalSettingService.calculateListingPrice("io661", Math.max(0,  (int) (Double.parseDouble(io661PriceText) * 100)));

                    // 悠悠有品价格保持元格式（String类型）
//                    if (uuCheckbox.isSelected()) {
//                        uuPriceInYuan = Double.parseDouble(df.format(Double.parseDouble(uuPriceText)));
//                    }

                    // 打印用户输入的价格，用于调试
                    System.out.println("用户输入的IO661价格: " + io661PriceText + " 元，转换为: " + io661PriceInCents + " 分");
//                    if (uuCheckbox.isSelected()) {
//                        System.out.println("用户输入的UU价格: " + uuPriceText + " 元，保持为: " + uuPriceInYuan + " 元");
//                    }

                    // 处理合并物品和单个物品的情况
                    if (item.getIds() != null && !item.getIds().isEmpty()) {
                        // 合并物品：ids是逗号分隔的字符串，需要拆分
                        System.out.println("处理合并物品: " + itemName + ", IDs: " + item.getIds());
                        String[] idArray = item.getIds().split(",");
                        for (String id : idArray) {
                            if (id != null && !id.trim().isEmpty()) {
                                ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                                inventoryItem.setId(Long.parseLong(id.trim()));
                                inventoryItem.setPrice(io661PriceInCents);
                                inventoryList.add(inventoryItem);
                                System.out.println("  - 添加合并物品ID: " + id.trim() + ", 价格: " + io661PriceInYuan + "元");
                            }
                        }
                    } else if (item.getId() != null) {
                        // 单个物品：使用物品ID
                        System.out.println("处理单个物品: " + itemName + ", ID: " + item.getId());
                        ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();


                        YouPinUserSellInventoryReq.DataItem inventoryYouPinItemList = new YouPinUserSellInventoryReq.DataItem();
                        // 悠品上架

                        inventoryYouPinItemList.setAssetId(Long.valueOf(item.getAssetId()));
                        // 悠悠有品价格使用String类型，单位为元，不是分
//                        inventoryYouPinItemList.setPrice(String.valueOf(uuPriceInYuan));
//                        inventoryYouPinList.setData(List.of(inventoryYouPinItemList));


                        // IO661上架
                        inventoryItem.setId(item.getId());
                        inventoryItem.setPrice(io661PriceInCents);
                        inventoryList.add(inventoryItem);
                        System.out.println("  - 添加单个物品ID: " + item.getId() + ", IO661价格: " + io661PriceInYuan + "元");
//                        if (uuCheckbox.isSelected()) {
//                            System.out.println("  - UU价格: " + uuPriceInYuan + "元");
//                        }
                    } else {
                        // 记录错误情况
                        System.out.println("警告：物品没有有效的ID：" + itemName);
                    }
                } catch (NumberFormatException e) {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的价格格式无效");
                    return;
                }
            }

            // 如果没有有效的物品要上架，显示提示并返回
            if (inventoryList.isEmpty()) {
                CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "上架提示", "没有有效的物品可以上架");
                return;
            }

            System.out.println("最终上架物品数量: " + inventoryList.size());
            request.setInventoryList(inventoryList);

            // 禁用确认按钮
            confirmButton.setDisable(true);
            confirmButton.setText("上架中...");

            // 在后台线程中发送请求
            new Thread(() -> {
                try {
                    // 发送IO661上架请求
                    ChangeOnSellStatusRes response = transactionAssistantService.changeOnSellStatus(request);

                    YouPinUserSellInventoryRes youPinUserSellInventoryRes = new YouPinUserSellInventoryRes();
                    YouPinUserItemsOnSellPriceChangeRes youPinPriceChangeRes = null;

                    // 如果选中悠悠有品上架
//                    if (uuCheckbox.isSelected()) {
//                        if (isPriceModifyMode) {
//                            // 价格修改模式：先获取在售数据，然后调用改价接口
//                            System.out.println("开始悠悠有品改价流程...");
//
//                            // 1. 先获取悠悠有品在售数据，获取正确的CommodityId
//                            YouPinUserInventoryOnSellDataListRes onSellDataRes = youPinService.getUserInventoryOnSellDataList(token);
//                            if (onSellDataRes == null || onSellDataRes.getData() == null ||
//                                    onSellDataRes.getData().getCommodityInfoList() == null) {
//                                System.out.println("获取悠悠有品在售数据失败，无法进行改价操作");
//                            } else {
//                                // 2. 构建改价请求
//                                YouPinUserItemsOnSellPriceChangeReq priceChangeReq = new YouPinUserItemsOnSellPriceChangeReq();
//                                priceChangeReq.setGameID(730);
//                                priceChangeReq.setSessionid(null); // 使用默认SessionId
//
//                                List<YouPinUserItemsOnSellPriceChangeReq.Commodity> commodities = new ArrayList<>();
//
//                                // 3. 遍历选中的物品，通过AssetId匹配获取CommodityId
//                                for (int i = 0; i < selectedItems.size(); i++) {
//                                    SteamInventoryRes item = selectedItems.get(i);
//                                    HBox itemRow = (HBox) itemsContainer.getChildren().get(i);
//
//                                    // 获取该行的价格输入框Map
//                                    @SuppressWarnings("unchecked")
//                                    Map<String, Map<String, TextField>> priceFields = (Map<String, Map<String, TextField>>) itemRow.getUserData();
//                                    Map<String, TextField> uuFields = priceFields.get("uu");
//                                    TextField uuReceivedPriceField = uuFields.get("received");
//
//                                    String uuPriceText = uuReceivedPriceField.getText();
//                                    if (uuPriceText != null && !uuPriceText.isEmpty()) {
//                                        try {
//                                            double uuPriceInYuan = Double.parseDouble(uuPriceText);
//
//                                            // 4. 通过AssetId在悠悠有品在售数据中查找对应的CommodityId
//                                            String commodityId = findCommodityIdByAssetId(item.getAssetId(), onSellDataRes.getData().getCommodityInfoList());
//
//                                            if (commodityId != null) {
//                                                YouPinUserItemsOnSellPriceChangeReq.Commodity commodity = new YouPinUserItemsOnSellPriceChangeReq.Commodity();
//                                                commodity.setCommodityId(commodityId);
//                                                commodity.setIsCanSold(true);
//                                                commodity.setOnlyChangeSaleInfo(true);
//                                                commodity.setRemark("");
//                                                // 悠悠有品价格使用String类型，单位为元
//                                                commodity.setPrice(String.format("%.2f", uuPriceInYuan));
//
//                                                commodities.add(commodity);
//                                                System.out.println("添加改价物品: AssetId=" + item.getAssetId() +
//                                                        ", CommodityId=" + commodityId + ", 新价格=" + uuPriceInYuan + "元");
//                                            } else {
//                                                System.out.println("未找到AssetId=" + item.getAssetId() + "对应的CommodityId，跳过改价");
//                                            }
//                                        } catch (NumberFormatException e) {
//                                            System.out.println("价格格式错误: " + uuPriceText);
//                                        }
//                                    }
//                                }
//
//                                // 5. 如果有需要改价的商品，调用改价接口
//                                if (!commodities.isEmpty()) {
//                                    priceChangeReq.setCommoditys(commodities);
//                                    youPinPriceChangeRes = youPinService.userItemsOnSellPriceChange(token, priceChangeReq);
//                                    System.out.println("悠悠有品改价请求完成，成功数量: " +
//                                            (youPinPriceChangeRes != null && youPinPriceChangeRes.getData() != null ?
//                                                    youPinPriceChangeRes.getData().getSuccessCount() : 0));
//                                } else {
//                                    System.out.println("没有找到可改价的商品");
//                                }
//                            }
//                        } else {
//                            // 上架模式：先上架，如果成功再考虑是否需要改价
//                            youPinUserSellInventoryRes = youPinService.userSellInventory(token, inventoryYouPinList);
//                            System.out.println("悠悠有品上架请求完成，成功数量: " +
//                                    (youPinUserSellInventoryRes != null && youPinUserSellInventoryRes.getData() != null ?
//                                            youPinUserSellInventoryRes.getData().size() : 0));
//                        }
//                    }

                    // TODO: 为BUFF上架预留接口调用
                    // if (buffCheckbox.isSelected()) {
                    //     // 调用BUFF平台上架接口

                    //   }

                    // 在JavaFX线程中处理响应
//                    YouPinUserSellInventoryRes finalYouPinUserSellInventoryRes = youPinUserSellInventoryRes;
//                    YouPinUserItemsOnSellPriceChangeRes finalYouPinPriceChangeRes = youPinPriceChangeRes;
                    Platform.runLater(() -> {
                        // 上架成功
                        int errorCount = response.getErrorList() != null ? response.getErrorList().size() : 0;
                        int successCount = inventoryList.size() - errorCount;

                        // 构建成功消息
                        StringBuilder message = new StringBuilder();

                        if (errorCount == 0) {
                            if (isPriceModifyMode) {
                                message.append("IO661成功修改 ").append(successCount).append(" 件物品的价格");
                            } else {
                                message.append("IO661成功上架 ").append(successCount).append(" 件物品");
                            }

                            // 如果选中了悠悠有品，添加悠悠有品的结果
//                            if (uuCheckbox.isSelected()) {
//                                if (isPriceModifyMode && finalYouPinPriceChangeRes != null) {
//                                    // 价格修改模式：显示改价结果
//                                    if (finalYouPinPriceChangeRes.getData() != null) {
//                                        int youPinSuccessCount = finalYouPinPriceChangeRes.getData().getSuccessCount() != null ?
//                                                finalYouPinPriceChangeRes.getData().getSuccessCount() : 0;
//                                        int youPinFailCount = finalYouPinPriceChangeRes.getData().getFailCount() != null ?
//                                                finalYouPinPriceChangeRes.getData().getFailCount() : 0;
//
//                                        if (youPinFailCount == 0) {
//                                            message.append("\n悠悠有品成功修改 ").append(youPinSuccessCount).append(" 件物品的价格");
//                                        } else {
//                                            message.append("\n悠悠有品成功修改 ").append(youPinSuccessCount).append(" 件物品的价格，失败 ").append(youPinFailCount).append(" 件物品");
//                                        }
//                                    } else {
//                                        message.append("\n悠悠有品改价请求失败");
//                                    }
//                                } else if (!isPriceModifyMode && finalYouPinUserSellInventoryRes.getData() != null) {
//                                    // 上架模式：显示上架结果
//                                    int youPinSuccessCount = finalYouPinUserSellInventoryRes.getData().size();
//                                    message.append("\n悠悠有品成功上架 ").append(youPinSuccessCount).append(" 件物品");
//                                }
//                            }

                            CommonShowAlert.showSyncAlert(Alert.AlertType.INFORMATION, isPriceModifyMode ? "修改价格成功" : "上架成功", message.toString());
                            closeDialog();
                        } else {
                            if (isPriceModifyMode) {
                                message.append("IO661成功修改 ").append(successCount).append(" 件物品的价格，失败 ").append(errorCount).append(" 件物品");
                            } else {
                                message.append("IO661成功上架 ").append(successCount).append(" 件物品，失败 ").append(errorCount).append(" 件物品");
                            }

                            // 如果选中了悠悠有品，添加悠悠有品的结果
//                            if (uuCheckbox.isSelected()) {
//                                if (isPriceModifyMode && finalYouPinPriceChangeRes != null) {
//                                    // 价格修改模式：显示改价结果
//                                    if (finalYouPinPriceChangeRes.getData() != null) {
//                                        int youPinSuccessCount = finalYouPinPriceChangeRes.getData().getSuccessCount() != null ?
//                                                finalYouPinPriceChangeRes.getData().getSuccessCount() : 0;
//                                        int youPinFailCount = finalYouPinPriceChangeRes.getData().getFailCount() != null ?
//                                                finalYouPinPriceChangeRes.getData().getFailCount() : 0;
//
//                                        if (youPinFailCount == 0) {
//                                            message.append("\n悠悠有品成功修改 ").append(youPinSuccessCount).append(" 件物品的价格");
//                                        } else {
//                                            message.append("\n悠悠有品成功修改 ").append(youPinSuccessCount).append(" 件物品的价格，失败 ").append(youPinFailCount).append(" 件物品");
//                                        }
//                                    } else {
//                                        message.append("\n悠悠有品改价请求失败");
//                                    }
//                                } else if (!isPriceModifyMode && finalYouPinUserSellInventoryRes.getData() != null) {
//                                    // 上架模式：显示上架结果
//                                    int youPinSuccessCount = finalYouPinUserSellInventoryRes.getData().size();
//                                    message.append("\n悠悠有品成功上架 ").append(youPinSuccessCount).append(" 件物品");
//                                }
//                            }

                            CommonShowAlert.showSyncAlert(Alert.AlertType.INFORMATION, isPriceModifyMode ? "修改价格部分成功" : "上架部分成功", message.toString());
                            closeDialog();
                        }
                    });
                } catch (Exception e) {
                    // 处理异常
                    Platform.runLater(() -> {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "上架异常", e.getMessage());
                        confirmButton.setDisable(false);
                        confirmButton.setText("确定上架");
                    });
                }
            }).start();
        } catch (Exception e) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "上架异常", "处理上架请求时发生错误: " + e.getMessage());
            confirmButton.setDisable(false);
            confirmButton.setText("确定上架");
        }
    }

    /**
     * 通过AssetId查找对应的CommodityId
     * @param assetId 要查找的AssetId
     * @param commodityInfoList 悠悠有品在售商品列表
     * @return 找到的CommodityId，如果未找到返回null
     */
    private String findCommodityIdByAssetId(String assetId, List<YouPinUserInventoryOnSellDataListRes.CommodityInfoList> commodityInfoList) {
        if (assetId == null || commodityInfoList == null) {
            return null;
        }

        for (YouPinUserInventoryOnSellDataListRes.CommodityInfoList commodity : commodityInfoList) {
            // 悠悠有品的steamAssetId与IO661的AssetId是相同的
            if (assetId.equals(commodity.getSteamAssetId())) {
                return String.valueOf(commodity.getId());
            }
        }

        System.out.println("未找到AssetId=" + assetId + "对应的CommodityId");
        return null;
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }
    /**
     * 一键定价
     */
    @FXML
    private void handleOneClickOnSale() {
        if (selectedItems == null || selectedItems.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "提示", "没有可定价的物品");
            return;
        }

        DecimalFormat df = new DecimalFormat("0.00");

        // 遍历所有行，为每个物品设置计算后的价格
        for (int i = 0; i < itemsContainer.getChildren().size(); i++) {
            HBox itemRow = (HBox) itemsContainer.getChildren().get(i);
            SteamInventoryResponse.InventoryDataList item = selectedItems.get(i);

            if (item.getRefPrice() == null || item.getRefPrice() <= 0) {
                System.out.println("物品 " + item.getItemName() + " 没有参考价格，跳过定价");
                continue;
            }

            // 获取该行的价格输入框Map
            @SuppressWarnings("unchecked")
            Map<String, Map<String, TextField>> priceFields = (Map<String, Map<String, TextField>>) itemRow.getUserData();
            Map<String, TextField> io661Fields = priceFields.get("io661");
            TextField io661ReceivedPriceField = io661Fields.get("received");

            // 计算参考价乘以比例
            double refPriceYuan = item.getRefPrice() / 100.0;
            double calculatedPrice = refPriceYuan * currentOneClickOnSaleRatio;

            // 设置到手价输入框
            io661ReceivedPriceField.setText(df.format(calculatedPrice));

            // 触发价格计算事件，更新上架价
            String oldValue = io661ReceivedPriceField.getText();
            io661ReceivedPriceField.textProperty().set(df.format(calculatedPrice));
        }

        CommonShowAlert.showSyncAlert(Alert.AlertType.INFORMATION, "定价完成", "已根据选择的比例完成所有物品定价");
    }
    /**
     * 设置一键定价的百分比
     */
    private void handleOneClickOnSaleRatio() {
        referencePriceComboBox.setOnAction(event -> {
            int selectedIndex = referencePriceComboBox.getSelectionModel().getSelectedIndex();
            switch (selectedIndex) {
                case 0:
                    currentOneClickOnSaleRatio = 1.00;
                    break;
                case 1:
                    currentOneClickOnSaleRatio = 1.10;
                    break;
                case 2:
                    currentOneClickOnSaleRatio = 1.01;
                    break;
                case 3:
                    currentOneClickOnSaleRatio = 0.995;
                    break;
                case 4:
                    currentOneClickOnSaleRatio = 0.99;
                    break;
                case 5:
                    // 自定义比例，打开输入对话框
                    handleOneClickOnSaleRatioCustomize();
                    break;
                default:
                    currentOneClickOnSaleRatio = 1.00;
                    break;
            }
        });
    }

    /**
     * 设置一键定价自定义输入
     */
    private void handleOneClickOnSaleRatioCustomize() {
        // 打开一个对话框，让用户输入自定义的百分比

        TextInputDialog dialog = new TextInputDialog(String.valueOf(currentOneClickOnSaleRatioCustomize));
        dialog.setTitle("自定义参考价比例");
        dialog.setHeaderText("请输入参考价比例（0.97~1.1）");
        dialog.setContentText("参考价比例:");

        // 显示确认对话框
        // ========== 1. 内容区域图标（原逻辑，可保留或调整） ==========
        ImageView contentIcon = new ImageView(new Image(
                Objects.requireNonNull(CommonShowAlert.class.getResourceAsStream(DEFAULT_ICON))
        ));
        contentIcon.setFitWidth(48);  // 内容区图标尺寸
        contentIcon.setFitHeight(48);
        dialog.getDialogPane().setGraphic(contentIcon);

        // ========== 2. 标题栏图标（新增逻辑） ==========
        // 获取 Alert 对应的 Stage（窗口）
        Stage stage = (Stage) dialog.getDialogPane().getScene().getWindow();
        // 加载标题栏图标（可复用现有资源，或单独定义新图标）
        Image stageIcon = new Image(
                Objects.requireNonNull(CommonShowAlert.class.getResourceAsStream(DEFAULT_ICON))
        );
        // 设置窗口图标（支持多分辨率，这里只设一个）
        stage.getIcons().add(stageIcon);

        // ========== 3. 加载自定义CSS（原逻辑，保持样式美化） ==========
        URL cssUrl = CommonShowAlert.class.getResource(ALERT_CSS);
        if (cssUrl != null) {
            dialog.getDialogPane().getStylesheets().add(cssUrl.toExternalForm());
        } else {
            System.err.println("警告：未找到Alert样式文件: " + ALERT_CSS);
        }

        // 自适应大小
        dialog.getDialogPane().setMinHeight(Region.USE_PREF_SIZE);


        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                double ratio = Double.parseDouble(result.get());
                if (ratio >= 0.97 && ratio <= 1.1) {
                    currentOneClickOnSaleRatioCustomize = ratio;
                    currentOneClickOnSaleRatio = ratio;

                    // 更新下拉框显示
                    referencePriceComboBox.getSelectionModel().select(5);
                } else {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "输入错误", "比例必须在0.97~1.1之间");
                }
            } catch (NumberFormatException e) {
                CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "输入错误", "请输入有效的数字");
            }
        }
    }
}

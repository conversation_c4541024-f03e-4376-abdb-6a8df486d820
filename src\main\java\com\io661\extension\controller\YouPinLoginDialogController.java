package com.io661.extension.controller;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.model.YouPin.YouPinSendSmsRes;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.service.YouPinService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import lombok.Getter;
import lombok.Setter;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;

/**
 * 悠悠有品登录对话框控制器
 */
public class YouPinLoginDialogController implements Initializable {

    @FXML
    private TextField phoneField;

    @FXML
    private Button sendCodeButton;

    @FXML
    private Button loginButton;

    @FXML
    private Button cancelButton;

    @FXML
    private VBox smsPromptBox;

    @FXML
    private Label smsPromptLabel;

    @FXML
    private Button cancelSmsButton;

    @FXML
    private Button confirmSmsButton;

    private YouPinService youPinService;

    @Setter
    private Consumer<Boolean> onLoginComplete;
    @Getter
    private String currentPhone;
    private String currentMsg;
    private String currentUpNumber;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        youPinService = new YouPinServiceImpl();

        // 设置手机号输入框只允许数字
        phoneField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                phoneField.setText(newValue.replaceAll("\\D", ""));
            }
            if (newValue.length() > 11) {
                phoneField.setText(newValue.substring(0, 11));
            }
        });
    }

    /**
     * 处理发送验证码按钮点击
     */
    @FXML
    private void handleSendCode() {
        String phone = phoneField.getText().trim();

        if (phone.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "提示", "请输入手机号");
        }

        if (phone.length() != 11) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "提示", "请输入正确的手机号");
            return;
        }

        currentPhone = phone;
        sendCodeButton.setDisable(true);
        sendCodeButton.setText("发送中...");

        // 在后台线程中发送验证码
        new Thread(() -> {
            try {
                YouPinSendSmsRes responseSendSms = youPinService.sendSmsCode(phone);

                Platform.runLater(() -> {
                    if (responseSendSms.getCode() == 0) {

                        currentMsg = responseSendSms.getData().getSmsUpContent();
                        currentUpNumber = responseSendSms.getData().getSmsUpNumber();

                        // 显示短信发送提示
                        showSmsPrompt();
                    } else {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "错误", "发送验证码失败，请重试");
                        resetSendCodeButton();
                    }
                });
            } catch (IOException e) {
                Platform.runLater(() -> {
                    CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "错误", "发送验证码失败: " + e.getMessage());
                    resetSendCodeButton();
                });
            }
        }).start();
    }

    /**
     * 显示短信发送提示
     */
    private void showSmsPrompt() {
        String promptText = String.format("请发送：\"%s\"到号码：%s", currentMsg, currentUpNumber);
        smsPromptLabel.setText(promptText);
        smsPromptBox.setVisible(true);
        smsPromptBox.setManaged(true);

        System.out.println("请发送：\"" + currentMsg + "\"到号码：" + currentUpNumber);
    }

    /**
     * 处理取消短信按钮点击
     */
    @FXML
    private void handleCancelSms() {
        hideSmsPrompt();
        resetSendCodeButton();
    }

    /**
     * 处理确认已发送短信按钮点击
     */
    @FXML
    private void handleConfirmSms() {
        hideSmsPrompt();
        sendCodeButton.setText("已发送");

        String phone = phoneField.getText().trim();
        String code = "";

        if (phone.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "提示", "请输入手机号");
            return;
        }

        loginButton.setDisable(true);
        loginButton.setText("登录中...");

        // 在后台线程中执行登录
        new Thread(() -> {
            try {
                boolean success = youPinService.loginAccount(phone, code);

                Platform.runLater(() -> {
                    if (success) {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "成功", "悠悠有品账号绑定成功");
                        closeDialog();
                        if (onLoginComplete != null) {
                            onLoginComplete.accept(true);
                        }
                    } else {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "错误", "绑定失败，请检查绑定账号否属于IO661");
                        loginButton.setDisable(false);
                        loginButton.setText("登录");
                    }
                });
            } catch (IOException e) {
                CommonShowAlert.showAlert(Alert.AlertType.WARNING, "错误", "登录失败: " + e.getMessage());
            }
        }).start();


    }

    /**
     * 隐藏短信发送提示
     */
    private void hideSmsPrompt() {
        smsPromptBox.setVisible(false);
        smsPromptBox.setManaged(false);
    }

    /**
     * 重置发送验证码按钮
     */
    private void resetSendCodeButton() {
        sendCodeButton.setDisable(false);
        sendCodeButton.setText("发送验证码");
    }

    /**
     * 处理登录按钮点击
     */
    @FXML
    private void handleLogin() {
        String phone = phoneField.getText().trim();
        String code = "";

        if (phone.isEmpty()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "提示", "请输入手机号");
            return;
        }

        loginButton.setDisable(true);
        loginButton.setText("登录中...");

        // 在后台线程中执行登录
        new Thread(() -> {
            try {
                boolean success = youPinService.loginAccount(phone, code);

                Platform.runLater(() -> {
                    if (success) {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.WARNING, "成功", "悠悠有品账号绑定成功");
                        closeDialog();
                        if (onLoginComplete != null) {
                            onLoginComplete.accept(true);
                        }
                    } else {
                        CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "错误", "绑定失败，请检查绑定账号否属于IO661");
                    }
                });
            } catch (IOException e) {
                    CommonShowAlert.showAlert(Alert.AlertType.WARNING, "错误", "登录失败: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 处理取消按钮点击
     */
    @FXML
    private void handleCancel() {
        closeDialog();
        if (onLoginComplete != null) {
            onLoginComplete.accept(false);
        }
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
}
